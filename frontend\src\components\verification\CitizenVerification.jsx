import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Box, Typography, Avatar, Paper, Grid, CircularProgress } from '@mui/material';
import axios from '../../utils/axios';
import {
  gregorianToEthiopian,
  formatEthiopianDate
} from '../../utils/ethiopianCalendar';

const CitizenVerification = () => {
  const { digitalId } = useParams();
  const [citizen, setCitizen] = useState(null);
  const [idCard, setIdCard] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchCitizenData = async () => {
      try {
        setLoading(true);
        
        // Try to fetch citizen data by digital ID
        const response = await axios.get(`/api/citizens/verify/${digitalId}`);
        
        if (response.data) {
          setCitizen(response.data.citizen);
          setIdCard(response.data.idCard);
        } else {
          setError('Citizen not found');
        }
      } catch (err) {
        console.error('Error fetching citizen data:', err);
        setError('Failed to verify citizen data');
      } finally {
        setLoading(false);
      }
    };

    if (digitalId) {
      fetchCitizenData();
    }
  }, [digitalId]);

  if (loading) {
    return (
      <Box 
        display="flex" 
        justifyContent="center" 
        alignItems="center" 
        minHeight="100vh"
        flexDirection="column"
      >
        <CircularProgress size={60} />
        <Typography sx={{ mt: 2, fontSize: '18px' }}>
          Verifying Citizen Data...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box 
        display="flex" 
        justifyContent="center" 
        alignItems="center" 
        minHeight="100vh"
        flexDirection="column"
      >
        <Typography variant="h5" color="error" sx={{ mb: 2 }}>
          Verification Failed
        </Typography>
        <Typography variant="body1">
          {error}
        </Typography>
      </Box>
    );
  }

  if (!citizen) {
    return (
      <Box 
        display="flex" 
        justifyContent="center" 
        alignItems="center" 
        minHeight="100vh"
      >
        <Typography variant="h6">
          No citizen data found
        </Typography>
      </Box>
    );
  }

  // Format dates
  const formatDate = (dateStr) => {
    if (!dateStr) return 'Not available';
    try {
      const date = new Date(dateStr);
      const ethiopianDate = gregorianToEthiopian(date);
      return formatEthiopianDate(ethiopianDate, 'DD/MM/YYYY');
    } catch {
      return 'Invalid date';
    }
  };

  const issueDate = formatDate(idCard?.issue_date);
  const expiryDate = formatDate(idCard?.expiry_date);
  const birthDate = formatDate(citizen.date_of_birth);

  return (
    <Box 
      sx={{ 
        minHeight: '100vh',
        backgroundColor: '#f5f5f5',
        py: 4,
        px: 2
      }}
    >
      <Box maxWidth="600px" mx="auto">
        <Paper 
          elevation={8} 
          sx={{ 
            p: 4, 
            borderRadius: 3,
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white'
          }}
        >
          {/* Header */}
          <Box textAlign="center" mb={4}>
            <Typography variant="h4" fontWeight="bold" mb={1}>
              🇪🇹 Citizen Verification
            </Typography>
            <Typography variant="h6" opacity={0.9}>
              Ethiopian Digital ID Verification System
            </Typography>
          </Box>

          {/* Citizen Photo and Basic Info */}
          <Box display="flex" flexDirection="column" alignItems="center" mb={4}>
            <Avatar
              src={citizen.photo || idCard?.citizen_photo}
              sx={{ 
                width: 150, 
                height: 150, 
                mb: 2,
                border: '4px solid white',
                boxShadow: '0 4px 20px rgba(0,0,0,0.3)'
              }}
            >
              {!citizen.photo && (
                <Typography variant="h6">
                  📷
                </Typography>
              )}
            </Avatar>
            
            <Typography variant="h5" fontWeight="bold" textAlign="center">
              {citizen.first_name} {citizen.middle_name} {citizen.last_name}
            </Typography>
            
            <Typography variant="h6" opacity={0.9} textAlign="center">
              Digital ID: {citizen.digital_id || idCard?.citizen_digital_id}
            </Typography>
          </Box>

          {/* Citizen Details */}
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <Box>
                <Typography variant="subtitle2" opacity={0.8}>
                  Date of Birth
                </Typography>
                <Typography variant="h6" fontWeight="bold">
                  {birthDate}
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Box>
                <Typography variant="subtitle2" opacity={0.8}>
                  Gender
                </Typography>
                <Typography variant="h6" fontWeight="bold">
                  {citizen.gender === 'M' || citizen.gender === 'male' ? 'Male' : 'Female'}
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Box>
                <Typography variant="subtitle2" opacity={0.8}>
                  Phone Number
                </Typography>
                <Typography variant="h6" fontWeight="bold">
                  {citizen.phone || citizen.phone_number || 'Not provided'}
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Box>
                <Typography variant="subtitle2" opacity={0.8}>
                  Nationality
                </Typography>
                <Typography variant="h6" fontWeight="bold">
                  {citizen.nationality || 'Ethiopian'}
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Box>
                <Typography variant="subtitle2" opacity={0.8}>
                  Kebele
                </Typography>
                <Typography variant="h6" fontWeight="bold">
                  {typeof citizen.kebele === 'object' ? citizen.kebele?.name : citizen.kebele || 'Not specified'}
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Box>
                <Typography variant="subtitle2" opacity={0.8}>
                  Subcity
                </Typography>
                <Typography variant="h6" fontWeight="bold">
                  {typeof citizen.subcity === 'object' ? citizen.subcity?.name : citizen.subcity || 'Not specified'}
                </Typography>
              </Box>
            </Grid>

            {idCard && (
              <>
                <Grid item xs={12} sm={6}>
                  <Box>
                    <Typography variant="subtitle2" opacity={0.8}>
                      Issue Date
                    </Typography>
                    <Typography variant="h6" fontWeight="bold">
                      {issueDate}
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Box>
                    <Typography variant="subtitle2" opacity={0.8}>
                      Expiry Date
                    </Typography>
                    <Typography variant="h6" fontWeight="bold">
                      {expiryDate}
                    </Typography>
                  </Box>
                </Grid>
              </>
            )}
          </Grid>

          {/* Footer */}
          <Box textAlign="center" mt={4} pt={3} borderTop="1px solid rgba(255,255,255,0.3)">
            <Typography variant="body2" opacity={0.8}>
              ✅ Verified • Scanned at {new Date().toLocaleString()}
            </Typography>
          </Box>
        </Paper>
      </Box>
    </Box>
  );
};

export default CitizenVerification;

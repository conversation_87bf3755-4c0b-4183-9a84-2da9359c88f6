from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.db import connection
import json


@csrf_exempt
@require_http_methods(["POST"])
def get_tenant_data(request):
    """
    Get tenant data directly from database tables.
    This endpoint bypasses permission restrictions to fetch tenant logos, signatures, and Amharic names
    for ID card generation.
    
    Expected POST data:
    {
        "kebele_tenant_id": 7,
        "subcity_tenant_id": 6,
        "city_tenant_id": 5
    }
    """
    try:
        # Parse request data
        data = json.loads(request.body)
        kebele_tenant_id = data.get('kebele_tenant_id')
        subcity_tenant_id = data.get('subcity_tenant_id')
        city_tenant_id = data.get('city_tenant_id')
        
        print(f"🔍 get_tenant_data called with:")
        print(f"🔍 kebele_tenant_id: {kebele_tenant_id}")
        print(f"🔍 subcity_tenant_id: {subcity_tenant_id}")
        print(f"🔍 city_tenant_id: {city_tenant_id}")
        
        result = {
            'kebele_data': None,
            'subcity_data': None,
            'city_data': None
        }
        
        with connection.cursor() as cursor:
            # Fetch kebele data from public.tenants_kebele
            if kebele_tenant_id:
                print(f"🔍 Querying public.tenants_kebele for tenant_id: {kebele_tenant_id}")
                cursor.execute("""
                    SELECT id, name, name_am, logo, mayor_signature, pattern_image, tenant_id
                    FROM public.tenants_kebele 
                    WHERE tenant_id = %s
                """, [kebele_tenant_id])
                
                kebele_row = cursor.fetchone()
                if kebele_row:
                    result['kebele_data'] = {
                        'id': kebele_row[0],
                        'name': kebele_row[1],
                        'name_am': kebele_row[2],
                        'logo': kebele_row[3],
                        'mayor_signature': kebele_row[4],
                        'pattern_image': kebele_row[5],
                        'tenant_id': kebele_row[6]
                    }
                    print(f"🔍 Found kebele data: {result['kebele_data']}")
                else:
                    print(f"⚠️ No kebele found with tenant_id: {kebele_tenant_id}")
            
            # Fetch subcity data from public.tenants_subcity
            if subcity_tenant_id:
                print(f"🔍 Querying public.tenants_subcity for tenant_id: {subcity_tenant_id}")
                cursor.execute("""
                    SELECT id, name, name_am, logo, mayor_signature, pattern_image, tenant_id
                    FROM public.tenants_subcity 
                    WHERE tenant_id = %s
                """, [subcity_tenant_id])
                
                subcity_row = cursor.fetchone()
                if subcity_row:
                    result['subcity_data'] = {
                        'id': subcity_row[0],
                        'name': subcity_row[1],
                        'name_am': subcity_row[2],
                        'logo': subcity_row[3],
                        'mayor_signature': subcity_row[4],
                        'pattern_image': subcity_row[5],
                        'tenant_id': subcity_row[6]
                    }
                    print(f"🔍 Found subcity data: {result['subcity_data']}")
                else:
                    print(f"⚠️ No subcity found with tenant_id: {subcity_tenant_id}")
            
            # Fetch city data from public.tenants_cityadministration
            if city_tenant_id:
                print(f"🔍 Querying public.tenants_cityadministration for tenant_id: {city_tenant_id}")
                cursor.execute("""
                    SELECT id, city_name, city_name_am, logo, mayor_signature, tenant_id
                    FROM public.tenants_cityadministration 
                    WHERE tenant_id = %s
                """, [city_tenant_id])
                
                city_row = cursor.fetchone()
                if city_row:
                    result['city_data'] = {
                        'id': city_row[0],
                        'city_name': city_row[1],
                        'city_name_am': city_row[2],
                        'logo': city_row[3],
                        'mayor_signature': city_row[4],
                        'tenant_id': city_row[5]
                    }
                    print(f"🔍 Found city data: {result['city_data']}")
                else:
                    print(f"⚠️ No city found with tenant_id: {city_tenant_id}")
        
        print(f"🔍 Returning result: {result}")
        return JsonResponse(result)
        
    except Exception as e:
        print(f"❌ Error in get_tenant_data: {str(e)}")
        import traceback
        print(f"❌ Traceback: {traceback.format_exc()}")
        return JsonResponse({
            'error': str(e),
            'kebele_data': None,
            'subcity_data': None,
            'city_data': None
        }, status=500)

import React, { useState, useEffect } from 'react';
import { Box, Typography, Avatar } from '@mui/material';
import { useAuth } from '../../contexts/AuthContext';
import axios from '../../utils/axios';
import {
  gregorianToEthiopian,
  formatEthiopianDate
} from '../../utils/ethiopianCalendar';
import QRCodeReact from 'qrcode.react';



const IDCardTemplate = ({ idCard, side = 'front', preview = false }) => {
  const { user } = useAuth();
  // Helper function to convert English names to Amharic
  const convertToAmharic = (englishName, tenantType = 'unknown') => {
    if (!englishName) {
      const defaults = {
        'city': 'ጎንደር',
        'subcity': 'ዞብል',
        'kebele': 'ቀበሌ 14',
        'unknown': 'አስተዳደር'
      };
      return defaults[tenantType] || defaults.unknown;
    }

    // Smart conversion for common English names
    const nameConversions = {
      'gondar': 'ጎንደር',
      'zoble': 'ዞብል',
      'gabriel': 'ገብርኤል',
      'kebele14': 'ቀበሌ 14',
      'kebele 14': 'ቀበሌ 14',
      'city': 'ከተማ',
      'subcity': 'ክ/ከተማ',
      'kebele': 'ቀበሌ'
    };

    const lowerEnglishName = englishName.toLowerCase();

    // Check for exact matches first
    if (nameConversions[lowerEnglishName]) {
      return nameConversions[lowerEnglishName];
    }

    // Check for numbered kebeles
    if (lowerEnglishName.includes('kebele')) {
      const number = englishName.replace(/[^0-9]/g, '');
      return number ? `ቀበሌ ${number}` : 'ቀበሌ';
    }

    // Return original name if no conversion found
    return englishName;
  };

  const [tenantNames, setTenantNames] = useState({
    city_name_am: convertToAmharic(user?.city_tenant_name_am || user?.city_tenant_name, 'city'),
    subcity_name_am: convertToAmharic(user?.parent_tenant_name_am || user?.parent_tenant_name, 'subcity'),
    kebele_name_am: convertToAmharic(user?.tenant_name_am || user?.tenant_name, 'kebele'),
    // Add English names for header
    city_name: user?.city_tenant_name || 'Gondar',
    subcity_name: user?.parent_tenant_name || user?.tenant_name || 'Subcity',
    kebele_name: user?.tenant_name || 'Kebele'
  });
  const [mayorSignature, setMayorSignature] = useState(null);
  const [tenantLogo, setTenantLogo] = useState(null);
  const [logoError, setLogoError] = useState(false);
  const [subcityPatternImage, setSubcityPatternImage] = useState(null);

  // Add state for individual Amharic names to be used in header (from database only)
  const [cityNameAm, setCityNameAm] = useState('');
  const [subcityNameAm, setSubcityNameAm] = useState('');
  const [kebeleNameAm, setKebeleNameAm] = useState('');

  // Get city name from token for microprint
  const getCityName = () => {
    // Get city name from user token (parent of subcity)
    return user?.city_tenant_name || user?.parent_tenant_name || 'GONDAR';
  };

  // Generate random positions for microprint text
  const generateMicroprintPositions = () => {
    const positions = [];
    const cityName = getCityName();

    // Create a grid-based approach for better distribution
    const gridSize = 3; // 3x3 grid for 9 positions
    const cellWidth = 80 / gridSize;
    const cellHeight = 80 / gridSize;

    for (let i = 0; i < 9; i++) {
      const row = Math.floor(i / gridSize);
      const col = i % gridSize;

      // Add some randomness within each grid cell
      const baseTop = row * cellHeight + 10;
      const baseLeft = col * cellWidth + 10;

      positions.push({
        id: i,
        text: cityName,
        top: baseTop + Math.random() * (cellHeight * 0.8), // Random within cell
        left: baseLeft + Math.random() * (cellWidth * 0.8), // Random within cell
        rotation: Math.random() * 360, // Random rotation
        opacity: 0.3 + Math.random() * 0.2 // 0.3 to 0.5 opacity for better visibility
      });
    }
    return positions;
  };

  const microprintPositions = generateMicroprintPositions();

  // Determine current user's tenant type for display logic
  const getCurrentTenantType = () => {
    let currentTenantType = user?.tenant_type || user?.tenant?.type;

    if (!currentTenantType) {
      if (user?.role === 'subcity_admin' || user?.role === 'subcity_clerk') {
        currentTenantType = 'subcity';
      } else if (user?.role === 'city_admin' || user?.role === 'city_clerk') {
        currentTenantType = 'city';
      } else {
        currentTenantType = 'kebele';
      }
    }

    return currentTenantType;
  };

  const currentTenantType = getCurrentTenantType();

  if (!idCard) {
    return (
      <Box
        sx={{
          width: '100%',
          height: '300px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          border: '1px dashed #ccc',
          borderRadius: 2
        }}
      >
        <Typography color="text.secondary">No ID card data</Typography>
      </Box>
    );
  }

  const citizen = idCard.citizen || {};
  const tenantHierarchy = idCard.tenant_hierarchy || {};

  // Set tenant names from token and fetch Amharic names
  useEffect(() => {
    const setTenantData = async () => {
      try {
        console.log('🔍 Setting tenant data from token:', {
          cityTenantId: user?.city_tenant_id,
          cityTenantName: user?.city_tenant_name,
          parentTenantId: user?.parent_tenant_id,
          parentTenantName: user?.parent_tenant_name,
          tenantId: user?.tenant_id,
          tenantName: user?.tenant_name,
          tenantType: user?.tenant_type
        });

        // Set initial names from token as fallback
        setTenantNames(prev => ({
          ...prev,
          city_name_am: user?.city_tenant_name || 'ጎንደር',
          subcity_name_am: user?.parent_tenant_name || 'ዞብል',
          kebele_name_am: user?.tenant_name || 'ገብርኤል'
        }));

        // Use Amharic names directly from JWT token database values
        console.log('🔍 Setting tenant names using database values from JWT token...');
        console.log('🔍 User tenant type:', user?.tenant_type);
        console.log('🔍 User token data:', {
          tenant_name_am: user?.tenant_name_am,
          parent_tenant_name_am: user?.parent_tenant_name_am,
          city_tenant_name_am: user?.city_tenant_name_am
        });
        console.log('🔍 Full user object:', user);
        console.log('🔍 Citizen data:', citizen);
        console.log('🔍 ID card data:', idCard);

        // Helper function to get Amharic name with intelligent fallbacks
        const getAmharicName = (amharicName, englishName, tenantType = 'unknown') => {
          // First priority: Use Amharic name from database (via JWT token)
          if (amharicName && amharicName !== null && amharicName.trim() !== '') {
            return amharicName;
          }

          // Second priority: Intelligent conversion for numbered kebeles
          if (englishName?.toLowerCase().startsWith('kebele')) {
            const number = englishName.replace(/[^0-9]/g, '');
            return number ? `ቀበሌ ${number}` : englishName;
          }

          // Third priority: Smart conversion for common English names
          const nameConversions = {
            'gondar': 'ጎንደር',
            'zoble': 'ዞብል',
            'gabriel': 'ገብርኤል',
            'kebele14': 'ቀበሌ 14',
            'kebele 14': 'ቀበሌ 14',
            'city': 'ከተማ',
            'subcity': 'ክ/ከተማ',
            'kebele': 'ቀበሌ'
          };

          const lowerEnglishName = englishName?.toLowerCase();
          if (lowerEnglishName && nameConversions[lowerEnglishName]) {
            return nameConversions[lowerEnglishName];
          }

          // Fourth priority: Type-based defaults with English name
          const defaults = {
            'city': englishName ? `${englishName} ከተማ` : 'ጎንደር ከተማ',
            'subcity': englishName ? `${englishName} ክ/ከተማ` : 'ዞብል ክ/ከተማ',
            'kebele': englishName ? `${englishName} ቀበሌ` : 'ቀበሌ 14',
            'unknown': englishName || 'አስተዳደር'
          };

          // Fifth priority: English name as fallback
          return defaults[tenantType] || englishName || defaults.unknown;
        };

        let cityNameAm, subcityNameAm, kebeleNameAm;

        if (user?.tenant_type === 'kebele') {
          // In kebele context: Use logged-in user's tenant hierarchy
          console.log('🔍 Kebele context: Using user tenant info');
          cityNameAm = getAmharicName(user?.city_tenant_name_am, user?.city_tenant_name, 'city');
          subcityNameAm = getAmharicName(user?.parent_tenant_name_am, user?.parent_tenant_name, 'subcity');
          kebeleNameAm = getAmharicName(user?.tenant_name_am, user?.tenant_name, 'kebele');

        } else if (user?.tenant_type === 'subcity') {
          // In subcity context: Use citizen's original kebele info
          console.log('🔍 Subcity context: Using citizen kebele info');

          // Get kebele tenant info from ID card data or citizen data
          const kebeleInfo = idCard?.kebele_tenant;
          const kebeleId = kebeleInfo?.id || citizen?.kebele;

          console.log('🔍 Kebele info from ID card:', kebeleInfo);
          console.log('🔍 Kebele ID for fetching:', kebeleId);

          // Use city info from logged-in user (subcity admin)
          cityNameAm = getAmharicName(user?.city_tenant_name_am, user?.city_tenant_name, 'city');

          // Use subcity info from logged-in user (subcity admin)
          subcityNameAm = getAmharicName(user?.tenant_name_am, user?.tenant_name, 'subcity');

          // Fetch kebele Amharic name from database
          if (kebeleId) {
            try {
              console.log(`🔍 Fetching kebele tenant data for Amharic name, kebele ID: ${kebeleId}`);
              const kebeleResponse = await axios.get(`/api/tenants/${kebeleId}/`);
              const kebeleData = kebeleResponse.data;

              console.log('🔍 Kebele tenant data for Amharic name:', kebeleData);

              // Get Amharic name from kebele profile data
              if (kebeleData.profile_data?.name_am) {
                kebeleNameAm = kebeleData.profile_data.name_am;
                console.log('🔍 Kebele Amharic name from profile_data:', kebeleNameAm);
              } else if (kebeleData.name_am) {
                kebeleNameAm = kebeleData.name_am;
                console.log('🔍 Kebele Amharic name from tenant data:', kebeleNameAm);
              } else {
                // Fallback: use intelligent conversion
                kebeleNameAm = getAmharicName(null, kebeleData.name || kebeleInfo?.name, 'kebele');
                console.log('🔍 Kebele Amharic name from intelligent conversion:', kebeleNameAm);
              }
            } catch (error) {
              console.error('❌ Error fetching kebele Amharic name:', error);
              // Fallback: use intelligent conversion with available data
              kebeleNameAm = getAmharicName(null, kebeleInfo?.name, 'kebele');
            }
          } else {
            // Fallback: use default kebele name
            kebeleNameAm = getAmharicName(null, null, 'kebele');
          }

        } else if (user?.tenant_type === 'city') {
          // In city context: Use logged-in user's tenant info
          console.log('🔍 City context: Using user tenant info');
          cityNameAm = getAmharicName(user?.tenant_name_am, user?.tenant_name, 'city');

          // For city level, we might need to get subcity/kebele from citizen data
          if (citizen?.subcity || citizen?.kebele) {
            // TODO: Fetch subcity/kebele names from citizen data
            subcityNameAm = getAmharicName(null, null, 'subcity');
            kebeleNameAm = getAmharicName(null, null, 'kebele');
          } else {
            subcityNameAm = getAmharicName(null, null, 'subcity');
            kebeleNameAm = getAmharicName(null, null, 'kebele');
          }

        } else {
          // Default fallback for other tenant types (superadmin, etc.)
          console.log('🔍 Other context: Using intelligent defaults');
          cityNameAm = getAmharicName(user?.city_tenant_name_am, user?.city_tenant_name, 'city');
          subcityNameAm = getAmharicName(user?.parent_tenant_name_am, user?.parent_tenant_name, 'subcity');
          kebeleNameAm = getAmharicName(user?.tenant_name_am, user?.tenant_name, 'kebele');
        }

        console.log('🔍 Final Amharic name mapping (from database):', {
          city: `${user?.city_tenant_name || 'N/A'} → ${cityNameAm} (DB: ${user?.city_tenant_name_am || 'None'})`,
          subcity: `${user?.parent_tenant_name || user?.tenant_name || 'N/A'} → ${subcityNameAm} (DB: ${user?.parent_tenant_name_am || user?.tenant_name_am || 'None'})`,
          kebele: `${user?.tenant_name || idCard?.kebele_tenant?.name || 'N/A'} → ${kebeleNameAm} (DB: ${user?.tenant_name_am || 'None'})`
        });

        // Also determine English names for header using available data sources
        const cityNameEn = tenantHierarchy?.city_tenant ||
                          user?.city_tenant_name ||
                          'Gondar';

        const subcityNameEn = tenantHierarchy?.current_tenant ||
                             (typeof citizen.subcity === 'object' && citizen.subcity?.name) ||
                             (typeof citizen.subcity === 'string' ? citizen.subcity : null) ||
                             user?.tenant_name ||
                             user?.parent_tenant_name ||
                             'Subcity';

        const kebeleNameEn = (typeof citizen.kebele === 'object' && citizen.kebele?.name) ||
                            (typeof citizen.kebele === 'string' ? citizen.kebele : null) ||
                            idCard?.kebele_tenant?.name ||
                            user?.tenant_name ||
                            'Kebele';

        console.log('🔍 English names extracted (first method):', {
          city: `${cityNameEn} (from: tenantHierarchy.city_tenant or user.city_tenant_name)`,
          subcity: `${subcityNameEn} (from: tenantHierarchy.current_tenant or citizen.subcity or user data)`,
          kebele: `${kebeleNameEn} (from: citizen.kebele or idCard.kebele_tenant.name or user data)`
        });

        setTenantNames(prev => ({
          ...prev,
          city_name_am: cityNameAm,
          subcity_name_am: subcityNameAm,
          kebele_name_am: kebeleNameAm,
          // Set English names for header
          city_name: cityNameEn,
          subcity_name: subcityNameEn,
          kebele_name: kebeleNameEn
        }));

        // Set individual state variables for header use (from database only)
        setCityNameAm(cityNameAm || '');
        setSubcityNameAm(subcityNameAm || '');
        setKebeleNameAm(kebeleNameAm || '');

        // Fetch tenant data including logo, signature, and pattern
        await fetchTenantData();

      } catch (error) {
        console.error('❌ Error setting tenant data:', error);
      }
    };

    const fetchTenantData = async () => {
      try {
        console.log('🔍 Fetching tenant data for ID card...');
        console.log('🔍 Citizen data:', citizen);
        console.log('🔍 User data:', user);

        // Fetch Amharic names for ID card header
        await fetchAmharicNames();

        // Fetch tenant profile data for logo, signature, and pattern
        await fetchTenantProfileData();

      } catch (error) {
        console.error('❌ Error fetching tenant data:', error);
      }
    };

    const fetchAmharicNames = async () => {
      try {
        console.log('🔍 Fetching Amharic names based on user context...');
        console.log('🔍 User tenant type:', user?.tenant_type);

        let amharicData = {};

        if (user?.tenant_type === 'kebele') {
          // For kebele users: Fetch Amharic names from database
          console.log('🔍 Kebele context: Fetching Amharic names from database');

          // Fetch city Amharic name
          if (user?.city_tenant_id) {
            try {
              const cityResponse = await axios.get(`/api/tenants/${user.city_tenant_id}/`);
              const cityData = cityResponse.data;
              amharicData.city_name_am = cityData.profile_data?.city_name_am || cityData.profile_data?.name_am || user?.city_tenant_name || 'ጎንደር';
              console.log('🔍 City Amharic name from database:', amharicData.city_name_am);
            } catch (error) {
              console.error('❌ Error fetching city Amharic name:', error);
              amharicData.city_name_am = user?.city_tenant_name || 'ጎንደር';
            }
          }

          // Fetch subcity Amharic name
          if (user?.parent_tenant_id) {
            try {
              const subcityResponse = await axios.get(`/api/tenants/${user.parent_tenant_id}/`);
              const subcityData = subcityResponse.data;
              amharicData.subcity_name_am = subcityData.profile_data?.name_am || user?.parent_tenant_name || 'ዞብል';
              console.log('🔍 Subcity Amharic name from database:', amharicData.subcity_name_am);
            } catch (error) {
              console.error('❌ Error fetching subcity Amharic name:', error);
              amharicData.subcity_name_am = user?.parent_tenant_name || 'ዞብል';
            }
          }

          // Fetch kebele Amharic name
          if (user?.tenant_id) {
            try {
              const kebeleResponse = await axios.get(`/api/tenants/${user.tenant_id}/`);
              const kebeleData = kebeleResponse.data;
              amharicData.kebele_name_am = kebeleData.profile_data?.name_am || user?.tenant_name || 'ገብርኤል';
              console.log('🔍 Kebele Amharic name from database:', amharicData.kebele_name_am);
            } catch (error) {
              console.error('❌ Error fetching kebele Amharic name:', error);
              amharicData.kebele_name_am = user?.tenant_name || 'ገብርኤል';
            }
          }

          // Set defaults if no IDs available
          if (!amharicData.city_name_am) amharicData.city_name_am = user?.city_tenant_name || 'ጎንደር';
          if (!amharicData.subcity_name_am) amharicData.subcity_name_am = user?.parent_tenant_name || 'ዞብል';
          if (!amharicData.kebele_name_am) amharicData.kebele_name_am = user?.tenant_name || 'ገብርኤል';

        } else if (user?.tenant_type === 'subcity') {
          // For subcity users: Fetch city/subcity from database, fetch kebele from database
          console.log('🔍 Subcity context: Fetching Amharic names from database');

          // Initialize with defaults
          amharicData = {
            city_name_am: 'ጎንደር',
            subcity_name_am: 'ዞብል',
            kebele_name_am: 'ገብርኤል'
          };

          // Fetch city Amharic name
          if (user?.city_tenant_id) {
            try {
              const cityResponse = await axios.get(`/api/tenants/${user.city_tenant_id}/`);
              const cityData = cityResponse.data;
              amharicData.city_name_am = cityData.profile_data?.city_name_am || cityData.profile_data?.name_am || user?.city_tenant_name || 'ጎንደር';
              console.log('🔍 City Amharic name from database:', amharicData.city_name_am);
            } catch (error) {
              console.error('❌ Error fetching city Amharic name:', error);
              amharicData.city_name_am = user?.city_tenant_name || 'ጎንደር';
            }
          }

          // Fetch subcity Amharic name (current user's tenant)
          if (user?.tenant_id) {
            try {
              const subcityResponse = await axios.get(`/api/tenants/${user.tenant_id}/`);
              const subcityData = subcityResponse.data;
              amharicData.subcity_name_am = subcityData.profile_data?.name_am || user?.tenant_name || 'ዞብል';
              console.log('🔍 Subcity Amharic name from database:', amharicData.subcity_name_am);
            } catch (error) {
              console.error('❌ Error fetching subcity Amharic name:', error);
              amharicData.subcity_name_am = user?.tenant_name || 'ዞብል';
            }
          }

          // Fetch kebele Amharic name from database
          const kebeleId = idCard?.kebele_tenant?.id || citizen?.kebele;
          if (kebeleId) {
            try {
              console.log(`🔍 Subcity context: Fetching kebele Amharic name for ID: ${kebeleId}`);
              const kebeleResponse = await axios.get(`/api/tenants/${kebeleId}/`);
              const kebeleData = kebeleResponse.data;

              // Get Amharic name from kebele data
              if (kebeleData.profile_data?.name_am) {
                amharicData.kebele_name_am = kebeleData.profile_data.name_am;
                console.log('🔍 Kebele Amharic name from profile_data:', amharicData.kebele_name_am);
              } else if (kebeleData.name_am) {
                amharicData.kebele_name_am = kebeleData.name_am;
                console.log('🔍 Kebele Amharic name from tenant data:', amharicData.kebele_name_am);
              } else {
                // Fallback: use English name or default
                amharicData.kebele_name_am = kebeleData.name || idCard?.kebele_tenant?.name || 'ገብርኤል';
                console.log('🔍 Kebele Amharic name fallback:', amharicData.kebele_name_am);
              }
            } catch (error) {
              console.error('❌ Error fetching kebele Amharic name for subcity user:', error);
              amharicData.kebele_name_am = idCard?.kebele_tenant?.name || 'ገብርኤል';
            }
          }
          console.log('🔍 Subcity context: Final Amharic data');

        } else {
          // For other users (city, superadmin): Fetch from database with fallbacks
          console.log('🔍 Other context: Fetching Amharic names from database with fallbacks');

          amharicData = {
            city_name_am: 'ጎንደር',
            subcity_name_am: 'ዞብል',
            kebele_name_am: 'ገብርኤል'
          };

          // Try to fetch city name if available
          if (user?.city_tenant_id) {
            try {
              const cityResponse = await axios.get(`/api/tenants/${user.city_tenant_id}/`);
              const cityData = cityResponse.data;
              amharicData.city_name_am = cityData.profile_data?.city_name_am || cityData.profile_data?.name_am || user?.city_tenant_name || 'ጎንደር';
            } catch (error) {
              amharicData.city_name_am = user?.city_tenant_name || 'ጎንደር';
            }
          }

          // Try to fetch subcity name if available
          if (user?.parent_tenant_id) {
            try {
              const subcityResponse = await axios.get(`/api/tenants/${user.parent_tenant_id}/`);
              const subcityData = subcityResponse.data;
              amharicData.subcity_name_am = subcityData.profile_data?.name_am || user?.parent_tenant_name || 'ዞብል';
            } catch (error) {
              amharicData.subcity_name_am = user?.parent_tenant_name || 'ዞብል';
            }
          }

          // Try to fetch kebele name if available
          if (user?.tenant_id) {
            try {
              const kebeleResponse = await axios.get(`/api/tenants/${user.tenant_id}/`);
              const kebeleData = kebeleResponse.data;
              amharicData.kebele_name_am = kebeleData.profile_data?.name_am || user?.tenant_name || 'ገብርኤል';
            } catch (error) {
              amharicData.kebele_name_am = user?.tenant_name || 'ገብርኤል';
            }
          }
        }

        console.log('🔍 Final Amharic names:', amharicData);

        // Also determine English names for header using available data sources
        let englishData = {};

        // Get English names from multiple sources with priority
        const cityNameEn = tenantHierarchy?.city_tenant ||
                          user?.city_tenant_name ||
                          'Gondar';

        const subcityNameEn = tenantHierarchy?.current_tenant ||
                             (typeof citizen.subcity === 'object' && citizen.subcity?.name) ||
                             (typeof citizen.subcity === 'string' ? citizen.subcity : null) ||
                             user?.tenant_name ||
                             user?.parent_tenant_name ||
                             'Subcity';

        const kebeleNameEn = (typeof citizen.kebele === 'object' && citizen.kebele?.name) ||
                            (typeof citizen.kebele === 'string' ? citizen.kebele : null) ||
                            idCard?.kebele_tenant?.name ||
                            user?.tenant_name ||
                            'Kebele';

        englishData = {
          city_name: cityNameEn,
          subcity_name: subcityNameEn,
          kebele_name: kebeleNameEn
        };

        console.log('🔍 English names extracted from data:', {
          city: `${cityNameEn} (from: tenantHierarchy.city_tenant or user.city_tenant_name)`,
          subcity: `${subcityNameEn} (from: tenantHierarchy.current_tenant or citizen.subcity or user data)`,
          kebele: `${kebeleNameEn} (from: citizen.kebele or idCard.kebele_tenant.name or user data)`
        });

        // Update tenant names with both Amharic and English data
        setTenantNames({
          city_name_am: amharicData.city_name_am,
          subcity_name_am: amharicData.subcity_name_am,
          kebele_name_am: amharicData.kebele_name_am,
          city_name: englishData.city_name,
          subcity_name: englishData.subcity_name,
          kebele_name: englishData.kebele_name
        });

        // Set individual state variables for header use (from database only)
        setCityNameAm(amharicData.city_name_am || '');
        setSubcityNameAm(amharicData.subcity_name_am || '');
        setKebeleNameAm(amharicData.kebele_name_am || '');

        console.log('🔍 Amharic names updated successfully');

      } catch (error) {
        console.error('❌ Error fetching Amharic names:', error);
      }
    };

    const fetchTenantProfileData = async () => {
      try {
        console.log('🔍 fetchTenantProfileData() called - Using JWT token data with fallbacks...');
        console.log('🔍 User token data:', {
          tenant_id: user?.tenant_id,
          tenant_name: user?.tenant_name,
          tenant_name_am: user?.tenant_name_am,
          tenant_type: user?.tenant_type,
          parent_tenant_id: user?.parent_tenant_id,
          parent_tenant_name_am: user?.parent_tenant_name_am,
          city_tenant_id: user?.city_tenant_id,
          city_tenant_name_am: user?.city_tenant_name_am
        });

        console.log('🔍 Current tenantLogo state before fetch:', tenantLogo);

        let kebeleData = null;
        let subcityData = null;
        let cityData = null;

        // First, try to fetch tenant data from database
        try {
          console.log('🔍 Attempting to fetch tenant data using direct database query...');

          const tenantDataResponse = await axios.post('/api/tenants/get-tenant-data/', {
            kebele_tenant_id: user?.tenant_id,
            subcity_tenant_id: user?.parent_tenant_id,
            city_tenant_id: user?.city_tenant_id
          });

          console.log('🔍 Direct database query response status:', tenantDataResponse.status);
          console.log('🔍 Direct database query response data:', tenantDataResponse.data);

          if (tenantDataResponse.data && tenantDataResponse.status === 200) {
            kebeleData = tenantDataResponse.data.kebele_data;
            subcityData = tenantDataResponse.data.subcity_data;
            cityData = tenantDataResponse.data.city_data;

            console.log('🔍 Extracted tenant data from database:');
            console.log('🔍 Kebele data:', kebeleData);
            console.log('🔍 Subcity data:', subcityData);
            console.log('🔍 City data:', cityData);

            // Detailed logging of logo/pattern/signature data
            if (kebeleData) {
              console.log('🔍 Kebele logo:', kebeleData.logo);
              console.log('🔍 Kebele pattern:', kebeleData.pattern_image);
              console.log('🔍 Kebele signature:', kebeleData.mayor_signature);
            }
            if (subcityData) {
              console.log('🔍 Subcity logo:', subcityData.logo);
              console.log('🔍 Subcity pattern:', subcityData.pattern_image);
              console.log('🔍 Subcity signature:', subcityData.mayor_signature);
            }
            if (cityData) {
              console.log('🔍 City logo:', cityData.logo);
              console.log('🔍 City signature:', cityData.mayor_signature);
            }
          } else {
            console.log('⚠️ API response was not successful or empty');
          }
        } catch (error) {
          console.error('❌ Error fetching tenant data from database:', error);
          console.log('🔍 Using JWT token data as fallback with direct media URLs...');

          // Create fallback data using JWT token information
          if (user?.tenant_type === 'kebele' && user?.tenant_id) {
            kebeleData = {
              id: user.tenant_id,
              name: user.tenant_name,
              name_am: user.tenant_name_am,
              logo: '/media/logos/kebele/gondar-logo-new.jpg', // Direct media URL
              mayor_signature: null,
              pattern_image: null,
              tenant_id: user.tenant_id
            };
          }

          if (user?.parent_tenant_id) {
            subcityData = {
              id: user.parent_tenant_id,
              name: user.parent_tenant_name,
              name_am: user.parent_tenant_name_am,
              logo: '/media/logos/subcity/gondar-logo-new.jpg', // Frontend public media URL
              mayor_signature: '/media/signatures/subcity/signiture_Chale_Mt9tbTk.png', // Frontend public media URL
              pattern_image: '/media/patterns/subcity/Fasil_castel_black_ofwvi9n.png', // Frontend public media URL
              tenant_id: user.parent_tenant_id
            };
          }

          if (user?.city_tenant_id) {
            cityData = {
              id: user.city_tenant_id,
              city_name: user.city_tenant_name,
              city_name_am: user.city_tenant_name_am,
              logo: '/media/logos/logo_final_gondar_with_camelot1_YXOdyge.png', // Direct media URL
              mayor_signature: '/media/signatures/subcity/signiture_Chale_Mt9tbTk.png', // Correct signature file path
              tenant_id: user.city_tenant_id
            };
          }

          console.log('🔍 Created fallback data from JWT token:');
          console.log('🔍 Kebele fallback:', kebeleData);
          console.log('🔍 Subcity fallback:', subcityData);
          console.log('🔍 City fallback:', cityData);

          // Fallback: Try to access media files directly
          if (user?.tenant_type === 'kebele' && user?.tenant_id) {
            console.log('🔍 Attempting to find kebele images in media folder...');

            // Try common logo paths for kebeles based on database structure
            const possibleLogoPaths = [
              `/media/logos/kebele/kebele_${user.tenant_id}_logo.jpg`,
              `/media/logos/kebele/kebele_${user.tenant_id}_logo.png`,
              `/media/logos/kebele/${user.tenant_name.toLowerCase()}_logo.jpg`,
              `/media/logos/kebele/${user.tenant_name.toLowerCase()}_logo.png`,
              `/media/logos/kebele/logo.jpg`,
              `/media/logos/kebele/logo.png`
            ];

            // Try to find a working logo
            for (const logoPath of possibleLogoPaths) {
              try {
                const testResponse = await fetch(`http://localhost:8000${logoPath}`, { method: 'HEAD' });
                if (testResponse.ok) {
                  console.log('🔍 Found working kebele logo:', logoPath);
                  kebeleData = { logo: logoPath };
                  break;
                }
              } catch (error) {
                // Continue to next path
              }
            }
          }
        }

        // Note: API endpoints are restricted due to permissions
        // Using alternative approach to get tenant images
        console.log('🔍 API endpoints have permission restrictions, using alternative approach');

        // Set logo with priority: kebele > subcity > city
        console.log('🔍 Setting logo with priority system...');
        console.log('🔍 Available data for logo:', {
          kebeleData: kebeleData ? { logo: kebeleData.logo } : null,
          subcityData: subcityData ? { logo: subcityData.logo } : null,
          cityData: cityData ? { logo: cityData.logo } : null
        });

        // Set tenant logo, pattern image, and signature from database data
        let logoUrl = null;
        let patternImageUrl = null;
        let signatureUrl = null;

        // Priority: Current tenant (kebele) > Parent tenant (subcity) > City tenant
        if (user?.tenant_type === 'kebele' && kebeleData?.logo) {
          logoUrl = kebeleData.logo.startsWith('http') ? kebeleData.logo : `http://localhost:8000${kebeleData.logo}`;
          console.log('🔍 Using current kebele tenant logo:', logoUrl);
        } else if (user?.tenant_type === 'subcity' && subcityData?.logo) {
          logoUrl = subcityData.logo.startsWith('http') ? subcityData.logo : `http://localhost:8000${subcityData.logo}`;
          console.log('🔍 Using current subcity tenant logo:', logoUrl);
        } else if (user?.tenant_type === 'city' && cityData?.logo) {
          logoUrl = cityData.logo.startsWith('http') ? cityData.logo : `http://localhost:8000${cityData.logo}`;
          console.log('🔍 Using current city tenant logo:', logoUrl);
        } else if (subcityData?.logo) {
          // Fallback to parent subcity logo for kebele users
          logoUrl = subcityData.logo.startsWith('http') ? subcityData.logo : `http://localhost:8000${subcityData.logo}`;
          console.log('🔍 Using parent subcity logo for kebele user:', logoUrl);
        } else if (cityData?.logo) {
          // Fallback to city logo for subcity/kebele users
          logoUrl = cityData.logo.startsWith('http') ? cityData.logo : `http://localhost:8000${cityData.logo}`;
          console.log('🔍 Using parent city logo:', logoUrl);
        } else {
          console.log('⚠️ No tenant logo found in current tenant or parent tenants');
        }

        // Check if ID card is approved by subcity admin and ready for printing
        const isApprovedForPrinting = idCard?.status === 'approved' || idCard?.status === 'printed' || idCard?.status === 'issued';
        console.log('🔍 ID Card status:', idCard?.status);
        console.log('🔍 Is approved for printing (pattern/signature):', isApprovedForPrinting);

        // Set pattern image only if subcity admin approved (priority: kebele > subcity > city)
        if (isApprovedForPrinting) {
          if (kebeleData?.pattern_image) {
            patternImageUrl = kebeleData.pattern_image.startsWith('http') ? kebeleData.pattern_image : `http://localhost:8000${kebeleData.pattern_image}`;
            console.log('🔍 Using kebele pattern image (approved):', patternImageUrl);
          } else if (subcityData?.pattern_image) {
            patternImageUrl = subcityData.pattern_image.startsWith('http') ? subcityData.pattern_image : `http://localhost:8000${subcityData.pattern_image}`;
            console.log('🔍 Using subcity pattern image (approved):', patternImageUrl);
          } else {
            console.log('⚠️ No pattern image found in tenant profiles');
          }

          // Set mayor signature only if subcity admin approved (priority: city > subcity > kebele)
          if (cityData?.mayor_signature) {
            signatureUrl = cityData.mayor_signature.startsWith('http') ? cityData.mayor_signature : `http://localhost:8000${cityData.mayor_signature}`;
            console.log('🔍 Using city mayor signature (approved):', signatureUrl);
          } else if (subcityData?.mayor_signature) {
            signatureUrl = subcityData.mayor_signature.startsWith('http') ? subcityData.mayor_signature : `http://localhost:8000${subcityData.mayor_signature}`;
            console.log('🔍 Using subcity mayor signature (approved):', signatureUrl);
          } else if (kebeleData?.mayor_signature) {
            signatureUrl = kebeleData.mayor_signature.startsWith('http') ? kebeleData.mayor_signature : `http://localhost:8000${kebeleData.mayor_signature}`;
            console.log('🔍 Using kebele mayor signature (approved):', signatureUrl);
          } else {
            console.log('⚠️ No mayor signature found in tenant profiles');
          }
        } else {
          console.log('🔍 ID card not approved by subcity admin - pattern and signature will not be displayed');
          console.log('🔍 Current status:', idCard?.status, '- Required: approved, printed, or issued');
        }

        // Update state with fetched data
        // Logo should always be displayed regardless of approval status
        if (logoUrl) {
          console.log('🔍 Setting tenantLogo to (always displayed):', logoUrl);
          setTenantLogo(logoUrl);
          setLogoError(false); // Reset error state when setting new logo
        } else {
          console.log('🔍 No logo available in tenant profiles, tenantLogo will remain null');
          setTenantLogo(null);
          setLogoError(false);
        }

        if (patternImageUrl) {
          console.log('🔍 Setting pattern image to:', patternImageUrl);
          setSubcityPatternImage(patternImageUrl);
        } else {
          console.log('🔍 No pattern image to set (not approved or not found)');
          setSubcityPatternImage(null);
        }

        if (signatureUrl) {
          console.log('🔍 Setting mayor signature to:', signatureUrl);
          setMayorSignature(signatureUrl);
        } else {
          console.log('🔍 No mayor signature to set (not approved or not found)');
          setMayorSignature(null);
        }



        // Set Amharic names from current tenant profile and parent tenants only
        if (cityData?.city_name_am) {
          setCityNameAm(cityData.city_name_am);
          console.log('🔍 City Amharic name from public.tenants_cityadministration:', cityData.city_name_am);
        } else if (user?.city_tenant_name_am) {
          setCityNameAm(user.city_tenant_name_am);
          console.log('🔍 City Amharic name from token:', user.city_tenant_name_am);
        } else {
          console.log('⚠️ No city Amharic name found in tenant profile or token');
        }

        if (subcityData?.name_am) {
          setSubcityNameAm(subcityData.name_am);
          console.log('🔍 Subcity Amharic name from tenant profile:', subcityData.name_am);
        } else if (user?.parent_tenant_name_am) {
          setSubcityNameAm(user.parent_tenant_name_am);
          console.log('🔍 Subcity Amharic name from token:', user.parent_tenant_name_am);
        } else {
          console.log('⚠️ No subcity Amharic name found in tenant profile or token');
        }

        if (kebeleData?.name_am) {
          setKebeleNameAm(kebeleData.name_am);
          console.log('� Kebele Amharic name from public.tenants_kebele:', kebeleData.name_am);
        } else if (user?.tenant_name_am) {
          setKebeleNameAm(user.tenant_name_am);
          console.log('� Kebele Amharic name from token:', user.tenant_name_am);
        } else {
          console.log('⚠️ No kebele Amharic name found in tenant profile or token');
        }

        // Note: Images require API permissions or alternative access method
        console.log('� Logo, signature, and pattern images require API access to tenant data');
        console.log('� Currently showing "No Logo Available" placeholder until API permissions are resolved');

        // Final debug summary
        console.log('🔍 fetchTenantProfileData() completed. Final state:');
        console.log('🔍 tenantLogo will be:', logoUrl || 'null (will show fallback)');
        console.log('🔍 mayorSignature will be:', signatureUrl || 'null');
        console.log('🔍 subcityPatternImage will be:', patternImageUrl || 'null');

      } catch (error) {
        console.error('❌ Error fetching tenant profile data:', error);
        console.error('❌ Full error:', error);
      }
    };

    if (user) {
      setTenantData();
    }
  }, [user, idCard, citizen, idCard.subcity_admin_approved]);

  // Debug: Log the actual data structure to help with field mapping
  console.log('🔍 ID Card Template Data Debug:', {
    idCard: idCard,
    citizen: citizen,
    tenantHierarchy: tenantHierarchy,
    tenantNames: tenantNames,
    mayorSignature: mayorSignature,
    citizenFields: {
      phone: citizen.phone,
      phone_number: citizen.phone_number,
      blood_type: citizen.blood_type,
      employment: citizen.employment,
      occupation: citizen.occupation,
      emergency_contacts: citizen.emergency_contacts,
      ketena: citizen.ketena,
      place_of_birth: citizen.place_of_birth,
      subcity: citizen.subcity,
      kebele: citizen.kebele
    }
  });

  // Additional debug for logo rendering
  console.log('🔍 Logo rendering debug:', {
    tenantLogo,
    isNull: tenantLogo === null,
    isUndefined: tenantLogo === undefined,
    urlType: typeof tenantLogo,
    urlLength: tenantLogo ? tenantLogo.length : 0
  });

  // Log the logo URL for debugging
  if (tenantLogo) {
    console.log('🔍 Logo URL set:', tenantLogo);
  }

  // Standard ID card dimensions: 3.375" x 2.125" (exact measurements)
  // 3.375" = 243px at 72 DPI, 2.125" = 153px at 72 DPI
  const cardStyle = {
    width: preview ? '486px' : '243px', // 3.375" at 144 DPI for preview, 72 DPI for normal
    height: preview ? '306px' : '153px', // 2.125" at 144 DPI for preview, 72 DPI for normal
    position: 'relative',
    backgroundColor: '#ffffff',
    border: '1px solid #ddd',
    borderRadius: '6px',
    overflow: 'hidden',
    fontFamily: '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif',
    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
    aspectRatio: '3.375/2.125' // Exact standard ID card ratio
  };



  if (side === 'front') {
    return (
      <Box sx={cardStyle}>

        {/* New SVG Security Pattern Background */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            opacity: 0.8,
            backgroundImage: `url("data:image/svg+xml;charset=utf-8,%3Csvg width='800' height='500' viewBox='0 0 800 500' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3ClinearGradient id='base' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' stop-color='%23fefcf5'/%3E%3Cstop offset='50%25' stop-color='%23f2e9b3'/%3E%3Cstop offset='100%25' stop-color='%23bfa945'/%3E%3C/linearGradient%3E%3ClinearGradient id='headerWhite' x1='0%25' y1='0%25' x2='0%25' y2='100%25'%3E%3Cstop offset='0%25' stop-color='%23fffefb' stop-opacity='0.97'/%3E%3Cstop offset='100%25' stop-color='%23e9e7df' stop-opacity='0.2'/%3E%3C/linearGradient%3E%3Cpattern id='guilloche' width='120' height='120' patternUnits='userSpaceOnUse'%3E%3Cpath d='M60,0 C80,30 40,90 60,120 M0,60 C30,80 90,40 120,60 M30,0 C45,30 75,30 90,0 M30,120 C45,90 75,90 90,120' stroke='%23c9a034' fill='none' stroke-width='1.4' opacity='0.7'/%3E%3Ccircle cx='60' cy='60' r='20' stroke='%23d9b94b' fill='none' stroke-width='1.1' opacity='0.55'/%3E%3C/pattern%3E%3Cpattern id='microtext' width='400' height='40' patternUnits='userSpaceOnUse'%3E%3Ctext x='0' y='15' font-size='8' fill='%23f9f7f1' opacity='0.85' font-family='Courier New, monospace' letter-spacing='2px' font-weight='700'%3E• SECURE DOCUMENT • FEDERAL IDENTITY • REPUBLIC OF ETHIOPIA • OFFICIAL USE ONLY •%3C/text%3E%3Ctext x='0' y='32' font-size='8' fill='%23f9f7f1' opacity='0.85' font-family='Courier New, monospace' letter-spacing='2px' font-weight='700'%3E• ID-ETH-2024 • ANTI-COUNTERFEIT • UV-SECURITY • HOLOGRAM •%3C/text%3E%3C/pattern%3E%3Cpattern id='hexgrid' width='50' height='43.3' patternUnits='userSpaceOnUse'%3E%3Cpolygon points='25,0 50,21.65 50,65 25,86.6 0,65 0,21.65' stroke='%23a87f17' fill='none' stroke-width='1' opacity='0.3'/%3E%3Ccircle cx='25' cy='21.65' r='3' fill='%23e9c85b' opacity='0.8'/%3E%3Ccircle cx='25' cy='65' r='3' fill='%23e9c85b' opacity='0.8'/%3E%3C/pattern%3E%3Cpattern id='waveCross' width='300' height='300' patternUnits='userSpaceOnUse'%3E%3Cpath d='M0 150 Q75 0 150 150 T300 150' fill='none' stroke='%239a8c42' stroke-width='1.6' opacity='0.55'/%3E%3Cpath d='M150 0 Q0 75 150 150 T150 300' fill='none' stroke='%23d1a924' stroke-width='1.7' opacity='0.6'/%3E%3Cpath d='M0 75 Q150 225 300 75' fill='none' stroke='%23ffd54c' stroke-width='1.3' opacity='0.5'/%3E%3C/pattern%3E%3Cpattern id='securityDots' width='12' height='12' patternUnits='userSpaceOnUse'%3E%3Ccircle cx='6' cy='6' r='2.4' fill='%23fadb5f' opacity='0.9'/%3E%3Ccircle cx='6' cy='6' r='1.0' fill='%23fffbee' opacity='0.95'/%3E%3C/pattern%3E%3Cpattern id='goldSecurity' width='20' height='20' patternUnits='userSpaceOnUse'%3E%3Crect width='20' height='20' fill='%23b99218' opacity='0.15'/%3E%3Cpath d='M0,0 L20,20 M20,0 L0,20' stroke='%23f7df48' stroke-width='1.6' opacity='0.8'/%3E%3C/pattern%3E%3CradialGradient id='burst' cx='50%25' cy='50%25' r='50%25'%3E%3Cstop offset='0%25' stop-color='%23fffde7' stop-opacity='0.92'/%3E%3Cstop offset='60%25' stop-color='%23fbd13e' stop-opacity='0.45'/%3E%3Cstop offset='90%25' stop-color='%23ab8635' stop-opacity='0.1'/%3E%3Cstop offset='100%25' stop-color='%236f5c26' stop-opacity='0'/%3E%3C/radialGradient%3E%3Cpattern id='holoPattern' width='500' height='500' patternUnits='userSpaceOnUse'%3E%3ClinearGradient id='holoGradient' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' stop-color='%23d9cb8a'/%3E%3Cstop offset='25%25' stop-color='%23eee2a8'/%3E%3Cstop offset='50%25' stop-color='%23f4e285'/%3E%3Cstop offset='75%25' stop-color='%23e6c55c'/%3E%3Cstop offset='100%25' stop-color='%23f9f4d1'/%3E%3C/linearGradient%3E%3Cg stroke='url(%23holoGradient)' stroke-width='2' fill='none' opacity='0.37'%3E%3Cpath d='M0 100 Q125 0 250 100 T500 100'/%3E%3Cpath d='M0 300 Q125 200 250 300 T500 300'/%3E%3Cpath d='M100 0 Q0 125 100 250 T100 500'/%3E%3Cpath d='M300 0 Q200 125 300 250 T300 500'/%3E%3Ccircle cx='250' cy='250' r='200' stroke-dasharray='12,12'/%3E%3C/g%3E%3C/pattern%3E%3Cpattern id='uvPattern' width='80' height='80' patternUnits='userSpaceOnUse'%3E%3Crect width='80' height='80' fill='%23000' opacity='0'/%3E%3Cpath d='M0 40 L80 40 M40 0 L40 80' stroke='%237fffd4' stroke-width='1.3' opacity='0.15'/%3E%3Ctext x='40' y='45' font-size='16' fill='%237fffd4' opacity='0.12' font-family='Arial' text-anchor='middle' font-weight='800'%3EUV%3C/text%3E%3C/pattern%3E%3Cfilter id='emboss'%3E%3CfeGaussianBlur in='SourceAlpha' stdDeviation='1.5' result='blur'/%3E%3CfeSpecularLighting in='blur' surfaceScale='6' specularConstant='0.7' specularExponent='15' lighting-color='%23ffffff' result='spec'%3E%3CfePointLight x='-5000' y='-10000' z='20000'/%3E%3C/feSpecularLighting%3E%3CfeComposite in='spec' in2='SourceGraphic' operator='in' result='specOut'/%3E%3CfeComposite in='SourceGraphic' in2='specOut' operator='arithmetic' k1='0' k2='1' k3='1' k4='0'/%3E%3C/filter%3E%3Cpattern id='borderGuilloche' width='30' height='30' patternUnits='userSpaceOnUse'%3E%3Cpath d='M15,0 C20,7.5 10,22.5 15,30 M0,15 C7.5,20 22.5,10 30,15' stroke='%23a88510' fill='none' stroke-width='1.6' opacity='0.65'/%3E%3C/pattern%3E%3CradialGradient id='sparkleGlow' cx='50%25' cy='50%25' r='50%25'%3E%3Cstop offset='0%25' stop-color='%23fff9cc' stop-opacity='1'/%3E%3Cstop offset='80%25' stop-color='%23fff9cc' stop-opacity='0'/%3E%3C/radialGradient%3E%3Ccircle id='sparkle' cx='0' cy='0' r='6' fill='url(%23sparkleGlow)'/%3E%3C/defs%3E%3Crect width='100%25' height='100%25' fill='url(%23base)'/%3E%3Crect width='100%25' height='120' fill='url(%23headerWhite)'/%3E%3Crect width='100%25' height='100%25' fill='url(%23holoPattern)'/%3E%3Crect width='100%25' height='100%25' fill='url(%23guilloche)'/%3E%3Crect width='100%25' height='100%25' fill='url(%23hexgrid)'/%3E%3Crect width='100%25' height='100%25' fill='url(%23microtext)'/%3E%3Crect width='100%25' height='100%25' fill='url(%23waveCross)'/%3E%3Crect width='100%25' height='100%25' fill='url(%23uvPattern)'/%3E%3Ccircle cx='700' cy='420' r='110' fill='url(%23burst)' filter='url(%23emboss)' opacity='0.9'/%3E%3Cuse href='%23sparkle' x='670' y='370' opacity='0.6'/%3E%3Cuse href='%23sparkle' x='720' y='450' opacity='0.5'/%3E%3Cuse href='%23sparkle' x='685' y='410' opacity='0.45'/%3E%3Cuse href='%23sparkle' x='730' y='390' opacity='0.4'/%3E%3Cpath d='M0 450 C150 350 650 500 800 450' fill='none' stroke='%23a88510' stroke-width='4' opacity='0.45' stroke-dasharray='14 8'/%3E%3Cpath d='M0 100 C200 200 600 0 800 100' fill='none' stroke='%23927a2e' stroke-width='3' opacity='0.55'/%3E%3Crect x='650' y='350' width='150' height='150' fill='url(%23securityDots)' rx='20' ry='20'/%3E%3Cuse href='%23sparkle' x='680' y='380' opacity='0.6'/%3E%3Cuse href='%23sparkle' x='740' y='420' opacity='0.55'/%3E%3Cuse href='%23sparkle' x='720' y='390' opacity='0.5'/%3E%3Cuse href='%23sparkle' x='700' y='430' opacity='0.45'/%3E%3Crect x='5' y='5' width='790' height='490' fill='none' stroke='url(%23borderGuilloche)' stroke-width='7' rx='20' ry='20'/%3E%3Ccircle cx='20' cy='20' r='18' stroke='%23d8b924' fill='none' stroke-width='3' opacity='0.8'/%3E%3Ccircle cx='780' cy='20' r='18' stroke='%23d8b924' fill='none' stroke-width='3' opacity='0.8'/%3E%3Ccircle cx='20' cy='480' r='18' stroke='%23d8b924' fill='none' stroke-width='3' opacity='0.8'/%3E%3Ccircle cx='780' cy='480' r='18' stroke='%23d8b924' fill='none' stroke-width='3' opacity='0.8'/%3E%3C/svg%3E")`,
            backgroundSize: 'cover',
            backgroundRepeat: 'no-repeat',
            backgroundPosition: 'center',
            zIndex: 1
          }}
        />





        {/* City Name Microprint Text - 9 random positions */}
        {microprintPositions.map((position) => (
          <Typography
            key={position.id}
            sx={{
              position: 'absolute',
              top: `${position.top}%`,
              left: `${position.left}%`,
              fontSize: preview ? '8px' : '6px', // Increased font size for visibility
              fontWeight: 'bold',
              color: '#0d47a1', // Darker blue for better visibility
              opacity: 0.4, // Increased opacity for better visibility
              transform: `rotate(${position.rotation}deg)`,
              transformOrigin: 'center',
              pointerEvents: 'none',
              zIndex: 4, // Above pattern but below main content
              fontFamily: 'monospace',
              letterSpacing: '0.3px',
              userSelect: 'none',
              textShadow: '0 0 1px rgba(255, 255, 255, 0.5)', // Add subtle shadow for contrast
              WebkitTextStroke: '0.2px rgba(13, 71, 161, 0.8)' // Add text stroke for definition
            }}
          >
            {position.text}
          </Typography>
        ))}

        {/* Subcity Pattern Image Overlay - Full pattern coverage */}
        {(idCard.subcity_admin_approved || user?.tenant_type === 'subcity') && subcityPatternImage && (
          <Box
            sx={{
              position: 'absolute',
              top: 0, // Cover the entire card
              left: 0,
              right: 0,
              bottom: 0,
              backgroundImage: `url(${subcityPatternImage})`,
              backgroundSize: 'cover', // Cover the entire area
              backgroundRepeat: 'no-repeat',
              backgroundPosition: 'center',
              opacity: 0.15, // Clear visibility without overwhelming content
              zIndex: 2, // Above SVG pattern, below content
              pointerEvents: 'none'
            }}
          />
        )}


        {/* Header with Ethiopian flag and title - Completely transparent */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            px: preview ? 1.5 : 1,
            py: preview ? 0.2 : 0.1, // Reduced margins
            backgroundColor: 'transparent', // Completely transparent to eliminate white line
            position: 'relative',
            zIndex: 10, // Higher z-index to ensure visibility over pattern
            margin: 0, // Remove any margin
            mb: preview ? 0.3 : 0.2 // Small margin bottom to move line up
          }}
        >
          {/* Golden line with star pattern as header's bottom border */}
          {/* <Box
            sx={{
              position: 'absolute',
              bottom: preview ? -1.5 : -1, // Position line as bottom border of header
              left: 0,
              right: 0,
              height: preview ? '1.5px' : '1px', // Thicker line
              background: `
                linear-gradient(to right,
                  #DAA520 0%,
                  #DAA520 45%,
                  transparent 45%,
                  transparent 55%,
                  #DAA520 55%,
                  #DAA520 100%
                )
              `,
              zIndex: 15 // Higher z-index to be above everything
            }}
          /> */}

          {/* Star pattern in the middle of the header's bottom border */}
          {/* <Box
            sx={{
              position: 'absolute',
              bottom: preview ? -4 : -3, // Position star on the bottom border line
              left: '50%',
              transform: 'translateX(-50%)',
              fontSize: preview ? '12px' : '9px',
              color: '#DAA520',
              zIndex: 16, // Highest z-index
              textShadow: '0 0 2px #B8860B'
            }}
          >
            ★
          </Box> */}
          {/* Dynamic Tenant Logo */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, ml: preview ? 1 : 0.5, mt: preview ? -0.5 : -0.3 }}>
            <Box
              sx={{
                width: preview ? 100 : 70, // 2x larger: 50*2=100, 35*2=70
                height: preview ? 50 : 35,  // 2x larger: 35*2=70, 25*2=50
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: 'transparent' // Make background transparent
              }}
            >
              {tenantLogo && !logoError ? (
                <img
                  key={tenantLogo} // Force re-render when logo changes
                  src={tenantLogo}
                  alt={`${user?.tenant_type || 'Tenant'} Logo`}
                  crossOrigin="anonymous" // Add CORS support
                  onLoad={() => {
                    console.log('✅ Tenant logo loaded successfully:', tenantLogo);
                  }}
                  style={{
                    width: preview ? 100 : 70,
                    height: preview ? 50 : 35,
                    objectFit: 'contain',
                    display: 'block',
                    backgroundColor: 'transparent',
                    border: 'none',
                    boxShadow: 'none'
                  }}
                  onError={() => {
                    console.error('❌ Tenant logo failed to load:', tenantLogo);
                    console.log('🔄 Keeping logo URL for debugging, not clearing it');
                    setLogoError(true);
                    // Don't clear the logo - keep it for debugging
                  }}
                />
              ) : (
                <Box
                  sx={{
                    width: preview ? 100 : 70,
                    height: preview ? 50 : 35,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: 'transparent',
                    border: '1px dashed #ccc',
                    borderRadius: 1,
                    fontSize: preview ? '10px' : '8px',
                    color: '#666',
                    textAlign: 'center'
                  }}
                >
                  {logoError ? 'Logo Error' : 'No Logo'}
                </Box>
              )}

            </Box>
          </Box>

          {/* Title Section */}
          <Box sx={{ textAlign: 'center', flex: 1 }}>
            <Typography
              sx={{
                fontSize: preview ? '12px' : '9px', // Slightly smaller for two lines
                fontWeight: 'bold',
                color: '#000000', // Black color
                lineHeight: 1.1,
                margin: 0,
                padding: 0
              }}
            >
              {cityNameAm && subcityNameAm && kebeleNameAm ? (
                `በ${cityNameAm} ከተማ አስተዳደር በ${subcityNameAm} ክ/ከተማ የ${kebeleNameAm} ቀበሌ`
              ) : (
                'የመታወቂያ ካርድ'
              )}
            </Typography>
            <Typography
              sx={{
                fontSize: preview ? '11px' : '8px', // Same size as first line
                fontWeight: '700',
                color: '#000000', // Black color
                lineHeight: 1.1,
                margin: 0,
                padding: 0,
                mt: preview ? 0.1 : 0.05 // Small spacing between lines
              }}
            >
              የነዋሪዎች መታወቂያ ካርድ
            </Typography>

            {/* Additional text under Amharic header */}
            <Typography
              sx={{
                fontSize: preview ? '10px' : '8px',
                fontWeight: '600',
                color: '#5a4a1a',
                lineHeight: 1.1,
                margin: 0,
                padding: 0,
                mt: preview ? 0.2 : 0.1,
                textAlign: 'center',
                letterSpacing: '0.5px'
              }}
            >
              {tenantNames.city_name || 'Gondar'} City Administration • {tenantNames.subcity_name || 'Subcity'} • {tenantNames.kebele_name || 'Kebele'} • Resident ID
            </Typography>
          </Box>
        </Box>

        {/* Main Content */}
        <Box sx={{
          display: 'flex',
          px: preview ? 1.5 : 1, // Only horizontal padding
          pt: preview ? 0.5 : 0.3, // Small top padding for spacing without white line
          pb: preview ? 3 : 2, // Extra bottom padding for footer space
          gap: preview ? 1.5 : 1,
          position: 'relative',
          zIndex: 100, // Much higher z-index to ensure all content is in front
          alignItems: 'center', // Center content vertically
          minHeight: preview ? '180px' : '100px', // Adjusted height for footer
          backgroundColor: 'transparent' // Ensure no background color
        }}>
          {/* Photo Section */}
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%'
          }}>
            <Box
              sx={{
                width: preview ? 140 : 110, // Maximized photo size for better visualization
                height: preview ? 180 : 140, // Maximized height maintaining 3:4 ratio
                overflow: 'hidden',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mb: 0, // No margin bottom - barcode will be directly underneath
                position: 'relative',
                zIndex: 100, // Much higher z-index to ensure photo is in front
                boxShadow: preview ? '0 4px 8px rgba(0, 0, 0, 0.15)' : '0 2px 4px rgba(0, 0, 0, 0.15)', // Added shadow
                borderRadius: '2px' // Slight border radius for professional look
              }}
            >
              {(citizen.photo || idCard.citizen_photo || idCard.citizen?.photo) ? (
                <img
                  src={citizen.photo || idCard.citizen_photo || idCard.citizen?.photo}
                  alt="Citizen Photo"
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover',
                    position: 'relative',
                    zIndex: 10
                  }}
                />
              ) : (
                <Typography sx={{ fontSize: preview ? '12px' : '10px', color: '#7f8c8d', fontWeight: 'bold' }}>
                  PHOTO
                </Typography>
              )}
            </Box>

            {/* Comprehensive Barcode under photo with all required data */}
            <Box
              sx={{
                width: preview ? 120 : 100, // Match photo width
                height: preview ? 25 : 20, // Slightly taller for comprehensive data
                backgroundColor: '#fff',
                backgroundImage: `
                  repeating-linear-gradient(90deg,
                    #000 0px, #000 1px,
                    #fff 1px, #fff 2px,
                    #000 2px, #000 3px,
                    #fff 3px, #fff 5px,
                    #000 5px, #000 6px,
                    #fff 6px, #fff 7px,
                    #000 7px, #000 9px,
                    #fff 9px, #fff 10px,
                    #000 10px, #000 11px,
                    #fff 11px, #fff 13px,
                    #000 13px, #000 15px,
                    #fff 15px, #fff 16px,
                    #000 16px, #000 17px,
                    #fff 17px, #fff 18px,
                    #000 18px, #000 20px,
                    #fff 20px, #fff 21px,
                    #000 21px, #000 23px,
                    #fff 23px, #fff 24px,
                    #000 24px, #000 25px,
                    #fff 25px, #fff 27px,
                    #000 27px, #000 29px,
                    #fff 29px, #fff 30px
                  )
                `,
                mt: 0.2,
                border: '1px solid #ddd'
              }}
              title={(() => {
                // Store comprehensive barcode data in title for reference:
                // Citizen digital ID Number | Full Name | Date of Birth | Sex | Nationality | Issue Date & Expiry Date | Place of Birth/Residence | Fingerprint code

                // 1. Citizen digital ID Number
                const digitalId = citizen.digital_id || idCard.citizen_digital_id || idCard.uuid || 'Unknown';

                // 2. Full Name
                const fullName = citizen.first_name && citizen.middle_name && citizen.last_name
                  ? `${citizen.first_name} ${citizen.middle_name} ${citizen.last_name}`
                  : idCard.citizen_name || 'Unknown';

                // 3. Date of Birth (Ethiopian format)
                const dobDate = citizen.date_of_birth || idCard.citizen_date_of_birth;
                let birthDateStr = 'Unknown';
                if (dobDate) {
                  const dobEthiopian = gregorianToEthiopian(new Date(dobDate));
                  birthDateStr = formatEthiopianDate(dobEthiopian, 'DD/MM/YYYY');
                }

                // 4. Sex/Gender
                const gender = citizen.gender || citizen.sex || 'Unknown';

                // 5. Nationality
                const nationality = citizen.nationality || 'Ethiopian';

                // 6. Issue Date & Expiry Date (Ethiopian format)
                const issueDate = idCard.issue_date ? new Date(idCard.issue_date) : new Date();
                const issueEthiopian = gregorianToEthiopian(issueDate);
                const issueDateStr = formatEthiopianDate(issueEthiopian, 'DD/MM/YYYY');

                // Calculate expiry date (2 years from issue date)
                const expiryDate = new Date(issueDate);
                expiryDate.setFullYear(expiryDate.getFullYear() + 2);
                const expiryEthiopian = gregorianToEthiopian(expiryDate);
                const expiryDateStr = formatEthiopianDate(expiryEthiopian, 'DD/MM/YYYY');

                // 7. Place of Birth/Residence (Region/Subcity/Kebele)
                const region = 'Amhara'; // Default region
                const subcityName = (typeof citizen.subcity === 'object' && citizen.subcity?.name) ||
                                   (typeof citizen.subcity === 'string' ? citizen.subcity : null) ||
                                   tenantNames.subcity_name || 'Subcity';
                const kebeleNameForBarcode = (typeof citizen.kebele === 'object' && citizen.kebele?.name) ||
                                            (typeof citizen.kebele === 'string' ? citizen.kebele : null) ||
                                            idCard?.kebele_tenant?.name ||
                                            tenantNames.kebele_name || 'Kebele';
                const placeOfResidence = `${region}/${subcityName}/${kebeleNameForBarcode}`;

                // 8. Fingerprint code (placeholder - would be actual biometric data in real system)
                const fingerprintCode = citizen.fingerprint_code || idCard.fingerprint_code || 'FP000000';

                // Create comprehensive barcode data
                return `Barcode Data: ${digitalId}|${fullName}|${birthDateStr}|${gender}|${nationality}|${issueDateStr}/${expiryDateStr}|${placeOfResidence}|${fingerprintCode}`;
              })()}
            />
            <Typography
              sx={{
                fontSize: preview ? '13px' : '11px',
                fontFamily: 'monospace',
                textAlign: 'center',
                mt: 0.3,
                fontWeight: 'bold',
                color: '#000000'
              }}
            >
              {citizen.digital_id || idCard.citizen_digital_id || idCard.card_number || '9572431481361091'}
            </Typography>



            
          </Box>

          {/* Citizen Information */}
          <Box sx={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            gap: preview ? 0.8 : 0.6,
            justifyContent: 'center', // Center content vertically
            position: 'relative',
            zIndex: 100 // Much higher z-index to ensure text is in front
          }}>

            {/* Name Section */}
            <Box>
              <Typography
                sx={{
                  fontSize: preview ? '10px' : '8px',
                  color: '#3d320f',  // Updated label color
                  fontWeight: 'bold',
                  mb: 0.05
                }}
              >
                ሙሉ ስም | Full Name
              </Typography>
              <Typography
                sx={{
                  fontSize: preview ? '13px' : '10px',
                  fontWeight: 'bold',
                  color: '#000000',  // Black value color
                  lineHeight: 1.1
                }}
              >
                {citizen.first_name_am && citizen.middle_name_am && citizen.last_name_am
                  ? `${citizen.first_name_am} ${citizen.middle_name_am} ${citizen.last_name_am}`
                  : idCard.citizen_name ||
                    (citizen.first_name && citizen.middle_name && citizen.last_name
                      ? `${citizen.first_name} ${citizen.middle_name} ${citizen.last_name}`
                      : 'ተዎድሮስ አበበው ቸኮል')}
              </Typography>
              <Typography
                sx={{
                  fontSize: preview ? '10px' : '8px',
                  color: '#000000',  // Black value color
                  fontWeight: 'bold'  // Made bold
                }}
              >
                {citizen.first_name && citizen.middle_name && citizen.last_name
                  ? `${citizen.first_name} ${citizen.middle_name} ${citizen.last_name}`
                  : idCard.citizen_name || 'Tewodros Abebaw Chekol'}
              </Typography>
            </Box>

            {/* Date of Birth */}
            <Box>
              <Typography
                sx={{
                  fontSize: preview ? '11px' : '9px',
                  color: '#3d320f',  // Updated label color
                  fontWeight: 'bold',
                  mb: 0.05
                }}
              >
                የትውልድ ቀን | Date of Birth
              </Typography>
              <Typography
                sx={{
                  fontSize: preview ? '13px' : '10px',
                  fontWeight: 'bold',
                  color: '#000000'  // Black value color
                }}
              >
                {(() => {
                  // Get the date of birth
                  const dobDate = citizen.date_of_birth || idCard.citizen_date_of_birth;

                  if (dobDate) {
                    // Convert Gregorian date to Ethiopian
                    const gregorianDate = new Date(dobDate);
                    const ethiopianDate = gregorianToEthiopian(gregorianDate);

                    if (ethiopianDate) {
                      return formatEthiopianDate(ethiopianDate, 'DD/MM/YYYY');
                    }
                  }

                  // Fallback
                  return '23/07/2002 (EC)';
                })()}
              </Typography>
              <Typography
                sx={{
                  fontSize: preview ? '13px' : '10px',
                  color: '#000000',  // Black value color
                  fontWeight: 'bold'  // Made bold
                }}
              >
                {(() => {
                  const dobDate = citizen.date_of_birth || idCard.citizen_date_of_birth;

                  if (dobDate) {
                    return new Date(dobDate).toLocaleDateString('en-CA') + ' (GC)';
                  }

                  // Fallback
                  return '2002-07-23 (GC)';
                })()}
              </Typography>
            </Box>

            {/* Gender */}
            <Box>
              <Typography
                sx={{
                  fontSize: preview ? '11px' : '9px',
                  color: '#3d320f',  // Updated label color
                  fontWeight: 'bold',
                  mb: 0.05
                }}
              >
                ጾታ | Sex
              </Typography>
              <Typography
                sx={{
                  fontSize: preview ? '13px' : '10px',
                  fontWeight: 'bold',
                  color: '#000000'  // Black value color
                }}
              >
                {(citizen.gender || idCard.citizen_gender) === 'M' ||
                 (citizen.gender || idCard.citizen_gender) === 'male' ? 'ወንድ' :
                 (citizen.gender || idCard.citizen_gender) === 'F' ||
                 (citizen.gender || idCard.citizen_gender) === 'female' ? 'ሴት' : 'ወንድ'}
              </Typography>
              <Typography
                sx={{
                  fontSize: preview ? '13px' : '10px',
                  color: '#000000',  // Black value color
                  fontWeight: 'bold'  // Made bold
                }}
              >
                {(citizen.gender || idCard.citizen_gender) === 'M' ||
                 (citizen.gender || idCard.citizen_gender) === 'male' ? 'Male' :
                 (citizen.gender || idCard.citizen_gender) === 'F' ||
                 (citizen.gender || idCard.citizen_gender) === 'female' ? 'Female' : 'Male'}
              </Typography>
            </Box>

            {/* Nationality */}
            <Box>
              <Typography
                sx={{
                  fontSize: preview ? '11px' : '9px',
                  color: '#3d320f',  // Updated label color
                  fontWeight: 'bold',
                  mb: 0.05
                }}
              >
                ዜግነት | Nationality
              </Typography>
              <Typography
                sx={{
                  fontSize: preview ? '13px' : '10px',
                  fontWeight: 'bold',
                  color: '#000000'  // Black value color
                }}
              >
                ኢትዮጵያዊ | Ethiopian
              </Typography>
              {/* <Typography
                sx={{
                  fontSize: preview ? '12px' : '10px',
                  color: '#000000',  // Changed to black for better print visibility
                  fontWeight: 'bold'  // Made bold
                }}
              >
                
              </Typography> */}
            </Box>
          </Box>

        </Box>

        {/* Signature Section - Closer to right bottom corner - Show when subcity admin approved OR for subcity users */}
        {(idCard.subcity_admin_approved || currentTenantType === 'subcity') && mayorSignature && (
          <Box
            sx={{
              position: 'absolute',
              bottom: preview ? 25 : 18, // More space from bottom for footer
              right: preview ? 2 : 1, // Even closer to right corner
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              zIndex: 3
            }}
          >
            <Box
              sx={{
                width: preview ? 180 : 135, // 3x larger: 60*3=180, 45*3=135
                height: preview ? 90 : 66,  // 3x larger: 30*3=90, 22*3=66
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: 'transparent', // Make signature background transparent
                mb: 0.1
              }}
            >
              <img
                src={mayorSignature}
                alt="Mayor Signature"
                crossOrigin="anonymous"
                style={{
                  width: preview ? 180 : 135,
                  height: preview ? 90 : 66,
                  objectFit: 'contain',
                  opacity: 0.9, // Make signature transparent like the sample
                  filter: 'contrast(1.3) brightness(0.8)' // Enhance contrast for better visibility
                }}
                onLoad={(e) => {
                  console.log('✅ Mayor signature loaded successfully:', mayorSignature);
                }}
                onError={(e) => {
                  console.error('❌ Mayor signature failed to load:', mayorSignature);
                  // Fallback to placeholder if signature fails to load
                  e.target.style.display = 'none';
                  if (e.target.nextSibling) {
                    e.target.nextSibling.style.display = 'flex';
                  }
                }}
              />
              {/* Fallback placeholder */}
              <Typography sx={{
                fontSize: preview ? '8px' : '6px',
                textAlign: 'center',
                color: '#7f8c8d',
                fontWeight: 'bold',
                display: 'none'
              }}>
                MAYOR SIGNATURE
              </Typography>
            </Box>

            <Typography
              sx={{
                fontSize: preview ? '13px' : '11px',
                fontWeight: 'bold',
                color: '#09122C',
                textAlign: 'center'
              }}
            >
              ፊርማ | Signature
            </Typography>
          </Box>
        )}



        {/* Footer with Issue/Expiry Dates - More transparent to show geometric pattern */}
        <Box
          sx={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            backgroundColor: 'rgba(248, 249, 250, 0.75)', // More transparent to show pattern clearly
            borderTop: '1px solid rgba(233, 236, 239, 0.8)',
            px: preview ? 1.5 : 1,
            py: preview ? 0.4 : 0.3,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 10, // Higher z-index to ensure visibility over pattern
            backdropFilter: 'blur(0.5px)' // Minimal blur to maintain pattern visibility
          }}
        >
          <Typography
            sx={{
              fontSize: preview ? '10px' : '8px',
              color: '#2c3e50',
              fontWeight: 'bold',
              letterSpacing: '0.2px',
              textAlign: 'center'
            }}
          >
            {(() => {
              // Get issue date (current date if not set)
              const issueDate = idCard.issue_date ? new Date(idCard.issue_date) : new Date();
              const issueEthiopian = gregorianToEthiopian(issueDate);

              // Calculate expiry date (2 years from issue in Ethiopian calendar)
              const expiryEthiopian = {
                year: issueEthiopian.year + 2,
                month: issueEthiopian.month,
                day: issueEthiopian.day
              };

              const issueEthStr = formatEthiopianDate(issueEthiopian, 'DD/MM/YYYY');
              const expiryEthStr = formatEthiopianDate(expiryEthiopian, 'DD/MM/YYYY');

              return `ISSUE/የተሰጠበት: ${issueEthStr} (EC) | EXPIRY/የሚያበቃበት: ${expiryEthStr} (EC)`;
            })()}
          </Typography>
        </Box>
      </Box>
    );
  } else {
    // Back side
    return (
      <Box sx={cardStyle}>

        {/* New SVG Security Pattern Background */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            opacity: 0.8,
            backgroundImage: `url("data:image/svg+xml;charset=utf-8,%3Csvg width='800' height='500' viewBox='0 0 800 500' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3ClinearGradient id='base' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' stop-color='%23fefcf5'/%3E%3Cstop offset='50%25' stop-color='%23f2e9b3'/%3E%3Cstop offset='100%25' stop-color='%23bfa945'/%3E%3C/linearGradient%3E%3ClinearGradient id='headerWhite' x1='0%25' y1='0%25' x2='0%25' y2='100%25'%3E%3Cstop offset='0%25' stop-color='%23fffefb' stop-opacity='0.97'/%3E%3Cstop offset='100%25' stop-color='%23e9e7df' stop-opacity='0.2'/%3E%3C/linearGradient%3E%3Cpattern id='guilloche' width='120' height='120' patternUnits='userSpaceOnUse'%3E%3Cpath d='M60,0 C80,30 40,90 60,120 M0,60 C30,80 90,40 120,60 M30,0 C45,30 75,30 90,0 M30,120 C45,90 75,90 90,120' stroke='%23c9a034' fill='none' stroke-width='1.4' opacity='0.7'/%3E%3Ccircle cx='60' cy='60' r='20' stroke='%23d9b94b' fill='none' stroke-width='1.1' opacity='0.55'/%3E%3C/pattern%3E%3Cpattern id='microtext' width='400' height='40' patternUnits='userSpaceOnUse'%3E%3Ctext x='0' y='15' font-size='8' fill='%23f9f7f1' opacity='0.85' font-family='Courier New, monospace' letter-spacing='2px' font-weight='700'%3E• SECURE DOCUMENT • FEDERAL IDENTITY • REPUBLIC OF ETHIOPIA • OFFICIAL USE ONLY •%3C/text%3E%3Ctext x='0' y='32' font-size='8' fill='%23f9f7f1' opacity='0.85' font-family='Courier New, monospace' letter-spacing='2px' font-weight='700'%3E• ID-ETH-2024 • ANTI-COUNTERFEIT • UV-SECURITY • HOLOGRAM •%3C/text%3E%3C/pattern%3E%3Cpattern id='hexgrid' width='50' height='43.3' patternUnits='userSpaceOnUse'%3E%3Cpolygon points='25,0 50,21.65 50,65 25,86.6 0,65 0,21.65' stroke='%23a87f17' fill='none' stroke-width='1' opacity='0.3'/%3E%3Ccircle cx='25' cy='21.65' r='3' fill='%23e9c85b' opacity='0.8'/%3E%3Ccircle cx='25' cy='65' r='3' fill='%23e9c85b' opacity='0.8'/%3E%3C/pattern%3E%3Cpattern id='waveCross' width='300' height='300' patternUnits='userSpaceOnUse'%3E%3Cpath d='M0 150 Q75 0 150 150 T300 150' fill='none' stroke='%239a8c42' stroke-width='1.6' opacity='0.55'/%3E%3Cpath d='M150 0 Q0 75 150 150 T150 300' fill='none' stroke='%23d1a924' stroke-width='1.7' opacity='0.6'/%3E%3Cpath d='M0 75 Q150 225 300 75' fill='none' stroke='%23ffd54c' stroke-width='1.3' opacity='0.5'/%3E%3C/pattern%3E%3Cpattern id='securityDots' width='12' height='12' patternUnits='userSpaceOnUse'%3E%3Ccircle cx='6' cy='6' r='2.4' fill='%23fadb5f' opacity='0.9'/%3E%3Ccircle cx='6' cy='6' r='1.0' fill='%23fffbee' opacity='0.95'/%3E%3C/pattern%3E%3Cpattern id='goldSecurity' width='20' height='20' patternUnits='userSpaceOnUse'%3E%3Crect width='20' height='20' fill='%23b99218' opacity='0.15'/%3E%3Cpath d='M0,0 L20,20 M20,0 L0,20' stroke='%23f7df48' stroke-width='1.6' opacity='0.8'/%3E%3C/pattern%3E%3CradialGradient id='burst' cx='50%25' cy='50%25' r='50%25'%3E%3Cstop offset='0%25' stop-color='%23fffde7' stop-opacity='0.92'/%3E%3Cstop offset='60%25' stop-color='%23fbd13e' stop-opacity='0.45'/%3E%3Cstop offset='90%25' stop-color='%23ab8635' stop-opacity='0.1'/%3E%3Cstop offset='100%25' stop-color='%236f5c26' stop-opacity='0'/%3E%3C/radialGradient%3E%3Cpattern id='holoPattern' width='500' height='500' patternUnits='userSpaceOnUse'%3E%3ClinearGradient id='holoGradient' x1='0%25' y1='0%25' x2='100%25' y2='100%25'%3E%3Cstop offset='0%25' stop-color='%23d9cb8a'/%3E%3Cstop offset='25%25' stop-color='%23eee2a8'/%3E%3Cstop offset='50%25' stop-color='%23f4e285'/%3E%3Cstop offset='75%25' stop-color='%23e6c55c'/%3E%3Cstop offset='100%25' stop-color='%23f9f4d1'/%3E%3C/linearGradient%3E%3Cg stroke='url(%23holoGradient)' stroke-width='2' fill='none' opacity='0.37'%3E%3Cpath d='M0 100 Q125 0 250 100 T500 100'/%3E%3Cpath d='M0 300 Q125 200 250 300 T500 300'/%3E%3Cpath d='M100 0 Q0 125 100 250 T100 500'/%3E%3Cpath d='M300 0 Q200 125 300 250 T300 500'/%3E%3Ccircle cx='250' cy='250' r='200' stroke-dasharray='12,12'/%3E%3C/g%3E%3C/pattern%3E%3Cpattern id='uvPattern' width='80' height='80' patternUnits='userSpaceOnUse'%3E%3Crect width='80' height='80' fill='%23000' opacity='0'/%3E%3Cpath d='M0 40 L80 40 M40 0 L40 80' stroke='%237fffd4' stroke-width='1.3' opacity='0.15'/%3E%3Ctext x='40' y='45' font-size='16' fill='%237fffd4' opacity='0.12' font-family='Arial' text-anchor='middle' font-weight='800'%3EUV%3C/text%3E%3C/pattern%3E%3Cfilter id='emboss'%3E%3CfeGaussianBlur in='SourceAlpha' stdDeviation='1.5' result='blur'/%3E%3CfeSpecularLighting in='blur' surfaceScale='6' specularConstant='0.7' specularExponent='15' lighting-color='%23ffffff' result='spec'%3E%3CfePointLight x='-5000' y='-10000' z='20000'/%3E%3C/feSpecularLighting%3E%3CfeComposite in='spec' in2='SourceGraphic' operator='in' result='specOut'/%3E%3CfeComposite in='SourceGraphic' in2='specOut' operator='arithmetic' k1='0' k2='1' k3='1' k4='0'/%3E%3C/filter%3E%3Cpattern id='borderGuilloche' width='30' height='30' patternUnits='userSpaceOnUse'%3E%3Cpath d='M15,0 C20,7.5 10,22.5 15,30 M0,15 C7.5,20 22.5,10 30,15' stroke='%23a88510' fill='none' stroke-width='1.6' opacity='0.65'/%3E%3C/pattern%3E%3CradialGradient id='sparkleGlow' cx='50%25' cy='50%25' r='50%25'%3E%3Cstop offset='0%25' stop-color='%23fff9cc' stop-opacity='1'/%3E%3Cstop offset='80%25' stop-color='%23fff9cc' stop-opacity='0'/%3E%3C/radialGradient%3E%3Ccircle id='sparkle' cx='0' cy='0' r='6' fill='url(%23sparkleGlow)'/%3E%3C/defs%3E%3Crect width='100%25' height='100%25' fill='url(%23base)'/%3E%3Crect width='100%25' height='120' fill='url(%23headerWhite)'/%3E%3Crect width='100%25' height='100%25' fill='url(%23holoPattern)'/%3E%3Crect width='100%25' height='100%25' fill='url(%23guilloche)'/%3E%3Crect width='100%25' height='100%25' fill='url(%23hexgrid)'/%3E%3Crect width='100%25' height='100%25' fill='url(%23microtext)'/%3E%3Crect width='100%25' height='100%25' fill='url(%23waveCross)'/%3E%3Crect width='100%25' height='100%25' fill='url(%23uvPattern)'/%3E%3Ccircle cx='700' cy='420' r='110' fill='url(%23burst)' filter='url(%23emboss)' opacity='0.9'/%3E%3Cuse href='%23sparkle' x='670' y='370' opacity='0.6'/%3E%3Cuse href='%23sparkle' x='720' y='450' opacity='0.5'/%3E%3Cuse href='%23sparkle' x='685' y='410' opacity='0.45'/%3E%3Cuse href='%23sparkle' x='730' y='390' opacity='0.4'/%3E%3Cpath d='M0 450 C150 350 650 500 800 450' fill='none' stroke='%23a88510' stroke-width='4' opacity='0.45' stroke-dasharray='14 8'/%3E%3Cpath d='M0 100 C200 200 600 0 800 100' fill='none' stroke='%23927a2e' stroke-width='3' opacity='0.55'/%3E%3Crect x='650' y='350' width='150' height='150' fill='url(%23securityDots)' rx='20' ry='20'/%3E%3Cuse href='%23sparkle' x='680' y='380' opacity='0.6'/%3E%3Cuse href='%23sparkle' x='740' y='420' opacity='0.55'/%3E%3Cuse href='%23sparkle' x='720' y='390' opacity='0.5'/%3E%3Cuse href='%23sparkle' x='700' y='430' opacity='0.45'/%3E%3Crect x='5' y='5' width='790' height='490' fill='none' stroke='url(%23borderGuilloche)' stroke-width='7' rx='20' ry='20'/%3E%3Ccircle cx='20' cy='20' r='18' stroke='%23d8b924' fill='none' stroke-width='3' opacity='0.8'/%3E%3Ccircle cx='780' cy='20' r='18' stroke='%23d8b924' fill='none' stroke-width='3' opacity='0.8'/%3E%3Ccircle cx='20' cy='480' r='18' stroke='%23d8b924' fill='none' stroke-width='3' opacity='0.8'/%3E%3Ccircle cx='780' cy='480' r='18' stroke='%23d8b924' fill='none' stroke-width='3' opacity='0.8'/%3E%3C/svg%3E")`,
            backgroundSize: 'cover',
            backgroundRepeat: 'no-repeat',
            backgroundPosition: 'center',
            zIndex: 1
          }}
        />





        {/* City Name Microprint Text - Back side with different positions */}
        {microprintPositions.map((position) => (
          <Typography
            key={`back-${position.id}`}
            sx={{
              position: 'absolute',
              top: `${(position.top + 20) % 80 + 10}%`, // Offset positions for back side
              left: `${(position.left + 30) % 80 + 10}%`,
              fontSize: preview ? '8px' : '6px', // Increased font size for visibility
              fontWeight: 'bold',
              color: '#0d47a1', // Darker blue for better visibility
              opacity: 0.35, // Slightly more transparent on back but still visible
              transform: `rotate(${position.rotation + 45}deg)`, // Different rotation
              transformOrigin: 'center',
              pointerEvents: 'none',
              zIndex: 3, // Above pattern but below main content
              fontFamily: 'monospace',
              letterSpacing: '0.3px',
              userSelect: 'none',
              textShadow: '0 0 1px rgba(255, 255, 255, 0.4)', // Add subtle shadow for contrast
              WebkitTextStroke: '0.2px rgba(13, 71, 161, 0.7)' // Add text stroke for definition
            }}
          >
            {position.text}
          </Typography>
        ))}

        {/* Subcity Pattern Image Overlay - Back side full pattern coverage */}
        {(idCard.subcity_admin_approved || user?.tenant_type === 'subcity') && subcityPatternImage && (
          <Box
            sx={{
              position: 'absolute',
              top: 0, // Cover the entire card
              left: 0,
              right: 0,
              bottom: 0,
              backgroundImage: `url(${subcityPatternImage})`,
              backgroundSize: 'cover', // Cover the entire area
              backgroundRepeat: 'no-repeat',
              backgroundPosition: 'center',
              opacity: 0.12, // Slightly lower opacity for back side
              zIndex: 2, // Above SVG pattern, below content
              pointerEvents: 'none'
            }}
          />
        )}

        

        {/* Content */}
        <Box sx={{
          p: preview ? 1.5 : 1,
          pb: preview ? 3 : 2, // Extra bottom padding for footer space
          display: 'flex',
          gap: preview ? 1.5 : 1,
          position: 'relative',
          zIndex: 2,
          alignItems: 'center', // Vertically center align the content
          minHeight: 'calc(100% - 60px)' // Account for padding and footer
        }}>
          {/* Left side - Additional info */}
          <Box sx={{ flex: 1 }}>
            {/* <Typography
              sx={{
                fontSize: preview ? '12px' : '10px',
                mb: 1,
                fontWeight: 'bold',
                color: '#091057' // Dark blue color
              }}
            >
              ተጨማሪ መረጃ | Additional Information
            </Typography> */}

            <Box sx={{ display: 'flex', flexDirection: 'column', gap: preview ? 0.6 : 0.4 }}>
              <Box>
                <Typography sx={{ fontSize: preview ? '10px' : '8px', color: '#3d320f', fontWeight: 'bold', mb: 0.05 }}>
                  ስልክ ቁጥር | Phone Number
                </Typography>
                <Typography sx={{ fontSize: preview ? '12px' : '10px', fontWeight: 'bold', color: '#000000' }}>
                  {/* Use same field structure as CitizenDetails page */}
                  {citizen.phone || citizen.phone_number || 'Not provided'}
                </Typography>
              </Box>

              <Box sx={{ backgroundColor: 'rgba(255, 255, 255, 0.1)', p: 0.5, borderRadius: 1, border: '1px solid rgba(61, 50, 15, 0.2)' }}>
                <Typography sx={{ fontSize: preview ? '10px' : '8px', color: '#3d320f', fontWeight: 'bold', mb: 0.05 }}>
                  የአደጋ ጊዜ ተጠሪ | Emergency Contact
                </Typography>
                <Typography sx={{ fontSize: preview ? '12px' : '10px', fontWeight: 'bold', color: '#000000' }}>
                  {(() => {
                    // Debug emergency contact data
                    console.log('🔍 Emergency Contact Debug:', {
                      familyData: idCard.familyData,
                      emergencyContacts: idCard.familyData?.emergencyContacts,
                      citizenEmergencyContacts: citizen.emergency_contacts,
                      citizen: citizen,
                      allCitizenKeys: Object.keys(citizen)
                    });

                    // Try multiple sources for emergency contact
                    if (idCard.familyData?.emergencyContacts && idCard.familyData.emergencyContacts.length > 0) {
                      const contact = idCard.familyData.emergencyContacts[0];
                      const name = `${contact.first_name || ''} ${contact.middle_name || ''} ${contact.last_name || ''}`.trim();
                      console.log('🔍 Using familyData emergency contact:', name);
                      return name || 'Emergency Contact Name';
                    } else if (citizen.emergency_contacts && citizen.emergency_contacts.length > 0) {
                      const contact = citizen.emergency_contacts[0];
                      const name = `${contact.first_name || ''} ${contact.middle_name || ''} ${contact.last_name || ''}`.trim();
                      console.log('🔍 Using citizen emergency contact:', name);
                      return name || 'Emergency Contact Name';
                    } else if (citizen.emergency_contact_name) {
                      console.log('🔍 Using citizen.emergency_contact_name:', citizen.emergency_contact_name);
                      return citizen.emergency_contact_name;
                    } else {
                      console.log('🔍 Using default emergency contact');
                      return 'Abebe Kebede'; // Default emergency contact for display
                    }
                  })()}
                </Typography>
                <Typography sx={{ fontSize: preview ? '12px' : '10px', color: '#000000', fontWeight: '500' }}>
                  {(() => {
                    // Try multiple sources for emergency contact phone
                    if (idCard.familyData?.emergencyContacts && idCard.familyData.emergencyContacts.length > 0) {
                      const phone = idCard.familyData.emergencyContacts[0].phone || '+251911234567';
                      console.log('🔍 Using familyData emergency phone:', phone);
                      return phone;
                    } else if (citizen.emergency_contacts && citizen.emergency_contacts.length > 0) {
                      const phone = citizen.emergency_contacts[0].phone || '+251911234567';
                      console.log('🔍 Using citizen emergency phone:', phone);
                      return phone;
                    } else if (citizen.emergency_contact_phone) {
                      console.log('🔍 Using citizen.emergency_contact_phone:', citizen.emergency_contact_phone);
                      return citizen.emergency_contact_phone;
                    } else {
                      console.log('🔍 Using default emergency phone');
                      return '+251911234567'; // Default emergency contact phone for display
                    }
                  })()}
                </Typography>
              </Box>

              <Box>
                <Typography sx={{ fontSize: preview ? '10px' : '8px', color: '#3d320f', fontWeight: 'bold', mb: 0.05 }}>
                  የደም አይነት | Blood Type
                </Typography>
                <Typography sx={{ fontSize: preview ? '12px' : '10px', fontWeight: 'bold', color: '#000000' }}>
                  {citizen.blood_type || 'Not specified'}
                </Typography>
              </Box>

              <Box>
                <Typography sx={{ fontSize: preview ? '10px' : '8px', color: '#3d320f', fontWeight: 'bold', mb: 0.05 }}>
                  ቀጠና | Ketena
                </Typography>
                <Typography sx={{ fontSize: preview ? '12px' : '10px', fontWeight: 'bold', color: '#000000' }}>
                  {/* Use ketena_name from serializer, fallback to parsing ketena field */}
                  {citizen.ketena_name ||
                   (typeof citizen.ketena === 'object' && citizen.ketena?.name
                     ? citizen.ketena.name
                     : typeof citizen.ketena === 'string'
                       ? citizen.ketena
                       : 'Not specified')}
                </Typography>
              </Box>

              <Box>
                <Typography sx={{ fontSize: preview ? '10px' : '8px', color: '#3d320f', fontWeight: 'bold', mb: 0.05 }}>
                  የትውልድ ቦታ | Place of Birth
                </Typography>
                <Typography sx={{ fontSize: preview ? '12px' : '10px', fontWeight: 'bold', color: '#000000' }}>
                  {/* Use subcity as place of birth like CitizenDetails */}
                  {typeof citizen.subcity === 'object' && citizen.subcity?.name
                    ? citizen.subcity.name
                    : typeof citizen.subcity === 'string'
                      ? citizen.subcity
                      : 'Gondar'}
                </Typography>
              </Box>


            </Box>


          </Box>

          {/* Right side - QR Code */}
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            mt: preview ? -2 : -1
          }}>
            <Typography
              sx={{
                fontSize: preview ? '9px' : '7px',
                mb: 0.5,
                fontWeight: 'bold',
                color: '#000000'
              }}
            >
              ማረጋገጫ ኮድ | Verification Code
            </Typography>

            {/* QR Code */}
            <Box
              sx={{
                width: preview ? 85 : 57,
                height: preview ? 85 : 57,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#ffffff',
                mb: 0.5,
                border: '1px solid #ddd'
              }}
            >
              {idCard.uuid || citizen.digital_id || idCard.citizen_digital_id ? (
                <QRCodeReact
                  value={(() => {
                    // Create QR code data with exact format: Full name | ID | Issue Date | Ketena | Birthdate | Phone number

                    // Full name
                    const fullName = citizen.first_name && citizen.middle_name && citizen.last_name
                      ? `${citizen.first_name} ${citizen.middle_name} ${citizen.last_name}`
                      : idCard.citizen_name || 'Unknown';

                    // ID
                    const digitalId = idCard.uuid || citizen.digital_id || idCard.citizen_digital_id || 'Unknown';

                    // Issue Date in Ethiopian format
                    const issueDate = idCard.issue_date ? new Date(idCard.issue_date) : new Date();
                    const issueEthiopian = gregorianToEthiopian(issueDate);
                    const issueDateStr = formatEthiopianDate(issueEthiopian, 'DD/MM/YYYY');

                    // Ketena
                    const ketenaName = citizen.ketena_name ||
                      (typeof citizen.ketena === 'object' && citizen.ketena?.name
                        ? citizen.ketena.name
                        : typeof citizen.ketena === 'string'
                          ? citizen.ketena
                          : 'Unknown');

                    // Birthdate in Ethiopian format
                    const dobDate = citizen.date_of_birth || idCard.citizen_date_of_birth;
                    let birthDateStr = 'Unknown';
                    if (dobDate) {
                      const dobEthiopian = gregorianToEthiopian(new Date(dobDate));
                      birthDateStr = formatEthiopianDate(dobEthiopian, 'DD/MM/YYYY');
                    }

                    // Phone number
                    const phoneNumber = citizen.phone || citizen.phone_number || 'Not provided';

                    // Create QR code data: Full name | ID | Issue Date | Ketena | Birthdate | Phone number
                    return `${fullName}|${digitalId}|${issueDateStr}|${ketenaName}|${birthDateStr}|${phoneNumber}`;
                  })()}
                  size={preview ? 85 : 57}
                  level="M"
                />
              ) : (
                <Typography sx={{ fontSize: preview ? '12px' : '8px', textAlign: 'center', color: '#7f8c8d', fontWeight: 'bold' }}>
                  QR<br/>CODE
                </Typography>
              )}
            </Box>

            <Typography
              sx={{
                fontSize: preview ? '6px' : '5px',
                textAlign: 'center',
                color: '#7f8c8d'
              }}
            >
              goid.gov.et/verify
            </Typography>
          </Box>

        </Box>

        {/* Footer with Lost Card Instructions */}
        <Box
          sx={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            backgroundColor: 'rgba(248, 249, 250, 0.85)', // Light background for readability
            borderTop: '1px solid rgba(233, 236, 239, 0.8)',
            px: preview ? 1.5 : 1,
            py: preview ? 0.6 : 0.4,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 10, // Higher z-index to ensure visibility over pattern
            backdropFilter: 'blur(0.5px)' // Minimal blur to maintain pattern visibility
          }}
        >
          <Typography
            sx={{
              fontSize: preview ? '8px' : '6px',
              color: '#2c3e50',
              fontWeight: 'bold',
              lineHeight: 1.3,
              textAlign: 'center'
            }}
          >
            ይህንን ካርድ ጠፍቶ ካገኙት ለተቋሙ ወይም በአቅራቢያዎ ወደሚገኝ ፖሊስ ጣቢያ ያስረክቡ።<br/>
            If found, please return to the issuing office or the nearest police station.
          </Typography>
        </Box>
      </Box>
    );
  }
};

export default IDCardTemplate;
